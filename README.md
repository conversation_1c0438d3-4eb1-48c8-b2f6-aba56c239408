# 都加算NAVI - 地方政府福利业务管理系统

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![.NET Framework](https://img.shields.io/badge/.NET%20Framework-4.7.2-blue.svg)
![Platform](https://img.shields.io/badge/platform-Windows-lightgrey.svg)

## 📋 项目概述

都加算NAVI是一个基于WPF开发的地方政府福利业务管理系统，专门用于管理社会保障相关数据。系统提供了直观的用户界面，支持Excel数据源的读取、编辑和管理，是地方政府福利部门的理想工具。

### 🎯 主要功能

- **📊 Excel数据管理** - 直接读取和操作Excel文件中的数据
- **📄 分页显示** - 支持大量数据的高效分页浏览
- **✏️ 数据编辑** - 完整的CRUD操作（新增、编辑、删除）
- **🔍 智能搜索** - 多字段模糊搜索和数据过滤
- **📅 日期处理** - 智能识别日期字段，提供日期选择器
- **🎨 现代化UI** - 基于Material Design的美观界面

### 📊 支持的数据类型

1. **事业者数据** - 事业者信息、联系方式、服务类别管理
2. **国保联数据** - 被保险者信息、认定状态、住所管理
3. **服务代码数据** - 服务代码、报酬标准、有效期管理

## 🚀 快速开始

### 系统要求

- **操作系统**: Windows 10/11
- **开发环境**: Visual Studio 2019/2022
- **运行时**: .NET Framework 4.7.2 或更高版本

### 安装步骤

1. **克隆项目**
   ```bash
   git clone [repository-url]
   cd jp-navi
   ```

2. **打开解决方案**
   ```bash
   # 使用Visual Studio打开
   start NAVI.sln
   ```

3. **还原NuGet包**
   - 在Visual Studio中右键解决方案 → "还原NuGet包"
   - 或使用命令行：`nuget restore NAVI.sln`

4. **构建项目**
   ```bash
   # Debug模式
   msbuild NAVI.sln /p:Configuration=Debug
   
   # Release模式
   msbuild NAVI.sln /p:Configuration=Release
   ```

5. **运行应用程序**
   - 按F5在Visual Studio中运行
   - 或运行生成的exe文件：`NAVI/bin/Debug/NAVI.exe`

## 🏗️ 项目结构

```
jp-navi/
├── NAVI.sln                    # Visual Studio解决方案文件
├── README.md                   # 项目说明文档
└── NAVI/                       # 主项目目录
    ├── Controls/               # 自定义控件
    │   └── PaginationControl.xaml(.cs)  # 分页控件
    ├── Models/                 # 数据模型
    │   ├── BusinessData.cs     # 事业者数据模型
    │   ├── NationalData.cs     # 国保联数据模型
    │   └── ServiceCodeData.cs  # 服务代码数据模型
    ├── Services/               # 业务服务层
    │   └── ExcelDataService.cs # Excel数据操作服务
    ├── Windows/                # 窗口
    │   └── EditWindow.xaml(.cs) # 通用编辑窗口
    ├── Styles/                 # 样式文件
    │   └── AppStyles.xaml      # 应用程序样式
    ├── database/               # 数据文件
    │   └── shortstay_app_ver0.9.xlsx  # Excel数据源
    ├── BusinessDataControl.xaml(.cs)    # 事业者数据管理界面
    ├── NationalDataControl.xaml(.cs)    # 国保联数据管理界面
    ├── ServiceCodeDataControl.xaml(.cs) # 服务代码数据管理界面
    ├── MainControl.xaml(.cs)            # 主控制界面
    ├── MainWindow.xaml(.cs)             # 主窗口
    ├── LoginControl.xaml(.cs)           # 登录界面
    ├── App.xaml(.cs)                    # 应用程序入口
    ├── NAVI.csproj                      # 项目文件
    └── README.md                        # 详细技术文档
```

## 🔧 技术栈

### 前端技术
- **WPF** - Windows Presentation Foundation界面框架
- **XAML** - 界面标记语言
- **Material Design** - 现代化UI主题库

### 数据处理
- **NPOI** - Excel文件读写操作库
- **动态数据绑定** - 支持灵活的数据结构

### 设计模式
- **MVVM模式** - 数据绑定和界面分离
- **控件复用** - 通用组件设计
- **服务层模式** - 业务逻辑封装

## 📦 依赖包

| 包名 | 版本 | 用途 |
|------|------|------|
| MaterialDesignThemes | 4.9.0 | Material Design主题 |
| MaterialDesignColors | 2.1.4 | Material Design颜色 |
| NPOI | 2.6.2 | Excel文件操作 |

## 💡 使用指南

### 1. 登录系统
- 启动应用程序后显示登录界面
- 输入用户ID和密码进行身份验证

### 2. 数据管理
- **浏览数据**: 左侧导航菜单选择数据类型
- **搜索过滤**: 使用搜索框进行关键字过滤
- **分页浏览**: 底部分页控件支持大数据量浏览

### 3. 数据操作
- **新增记录**: 点击"新增记录"按钮，填写表单
- **编辑记录**: 选择行后点击"编辑"按钮
- **删除记录**: 选择行后点击"删除"按钮并确认

### 4. 特殊功能
- **日期输入**: 日期字段自动提供日期选择器
- **实时保存**: 所有修改自动保存到Excel文件
- **数据验证**: 输入数据自动验证格式

## 🔍 核心特性

### Excel数据源集成
- 直接读取Excel工作表数据
- 动态列生成，无需硬编码
- 实时数据同步和保存

### 智能日期处理
- 自动识别日期字段（生年月日、认定年月日等）
- 提供直观的日期选择器
- 支持多种日期格式解析

### 高性能分页
- 大数据量的高效分页显示
- 可调整页面大小（10/20/50/100条）
- 页码跳转和导航功能

### 通用编辑窗口
- 动态字段生成
- 智能控件选择（文本框/下拉框/日期选择器）
- 美观的两列布局设计

## 🛠️ 开发指南

### 添加新数据类型
1. 在`Models/`目录创建新的数据模型类
2. 在主界面添加对应的控件
3. 在Excel文件中添加相应的工作表
4. 更新导航菜单

### 自定义字段类型
1. 修改`EditWindow.xaml.cs`中的`CreateInputControl()`方法
2. 添加新的控件类型判断逻辑
3. 实现相应的数据验证规则

### 扩展Excel操作
1. 在`ExcelDataService.cs`中添加新方法
2. 实现特定的数据处理逻辑
3. 更新错误处理机制

## 📚 文档

- [详细技术文档](NAVI/README.md) - 完整的技术实现说明
- [系统改进记录](NAVI/IMPROVEMENTS.md) - 功能改进和优化记录
- [性能优化指南](NAVI/PERFORMANCE_OPTIMIZATIONS.md) - 性能优化建议
- [问题修复记录](NAVI/PROBLEM_FIXES.md) - 已知问题和解决方案

## 🐛 故障排除

### 常见问题

**Q: Excel文件无法打开**
- 检查文件路径是否正确
- 确认文件权限设置
- 验证Excel文件格式

**Q: 数据保存失败**
- 确保Excel文件未被其他程序占用
- 检查磁盘空间是否充足
- 验证文件写入权限

**Q: 界面显示异常**
- 检查Material Design主题是否正确加载
- 验证.NET Framework版本
- 重新构建项目

## 🤝 贡献指南

1. Fork本项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建Pull Request

## 📄 许可证

本项目采用MIT许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🔄 版本历史

### v0.9 (当前版本)
- ✅ 完整的Excel数据管理功能
- ✅ 三种数据类型支持（事业者、国保联、服务代码）
- ✅ 智能日期字段识别和处理
- ✅ 分页控件和搜索功能
- ✅ Material Design现代化界面
- ✅ 通用编辑窗口和数据验证

### 计划功能
- 🔄 数据导入/导出功能增强
- 🔄 用户权限管理系统
- 🔄 报表生成功能
- 🔄 数据备份和恢复
- 🔄 多语言支持

## 🎯 应用场景

### 适用部门
- **地方政府福利部门** - 社会保障数据管理
- **社会保险机构** - 被保险者信息管理
- **福利服务机构** - 服务代码和报酬管理
- **相关事业单位** - 业务数据统一管理

### 典型用例
1. **日常数据维护** - 新增、修改、删除各类数据记录
2. **数据查询检索** - 快速查找特定条件的数据记录
3. **批量数据处理** - 通过Excel进行批量数据导入和处理
4. **报表数据准备** - 为上级部门准备标准化数据报表

## 🔒 安全特性

- **登录验证** - 用户身份认证机制
- **数据完整性** - 输入数据格式验证
- **文件保护** - Excel文件访问控制
- **操作日志** - 关键操作记录追踪

## 🚀 性能特点

- **高效分页** - 大数据量下的流畅浏览体验
- **实时搜索** - 即时数据过滤和查找
- **内存优化** - 合理的内存使用和垃圾回收
- **响应式UI** - 流畅的用户交互体验

## 📊 系统截图

*注：实际使用时可以添加系统界面截图*

- 登录界面
- 主控制面板
- 数据管理界面
- 编辑窗口界面

## 📞 联系方式

如有问题或建议，请通过以下方式联系：

- 项目Issues: [GitHub Issues](../../issues)
- 技术支持: [<EMAIL>]
- 项目维护: [<EMAIL>]

## 🙏 致谢

感谢以下开源项目的支持：
- [Material Design In XAML](http://materialdesigninxaml.net/) - 提供美观的UI组件
- [NPOI](https://github.com/tonyqus/npoi) - 强大的Excel操作库
- [.NET Framework](https://dotnet.microsoft.com/) - 稳定的开发平台

---

**都加算NAVI** - 让地方政府福利业务管理更简单、更高效！

*最后更新: 2024年*
