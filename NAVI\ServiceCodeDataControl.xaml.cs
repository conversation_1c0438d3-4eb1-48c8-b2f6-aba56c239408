using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Windows;
using NAVI.Utils;
using System.Data.SQLite;

namespace NAVI
{
    /// <summary>
    /// ServiceCodeDataControl.xaml 的交互逻辑
    /// </summary>
    public partial class ServiceCodeDataControl : UserControl
    {
        private ObservableCollection<ServiceCodeData> _serviceCodeDataList;
        private List<ServiceCodeData> _allData;
        private List<string> _columnNames;
        private DatabaseManager _databaseManager;
        private ServiceCodeRepository _serviceCodeRepository;
        private System.Windows.Threading.DispatcherTimer _searchTimer;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public ServiceCodeDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _serviceCodeRepository = _databaseManager.ServiceCodes;

                // 从数据库加载数据
                LoadDataFromDatabase();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从数据库加载数据（同步版本，保持兼容性）
        /// </summary>
        private void LoadDataFromDatabase()
        {
            try
            {
                // 设置列名（严格按照ServiceCodeMaster字段）
                _columnNames = new List<string>
                {
                    "サービスコード", "サービス内容略称", "障害支援区分", "合成単位", "級地コード",
                    "単位数単価", "国費単価", "旧身体療護", "都単価", "キーコード", "都加算単価"
                };

                CreateDynamicColumns();

                // 使用异步加载
                Task.Run(async () => await LoadPagedDataAsync());
            }
            catch (Exception ex)
            {
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private async Task LoadPagedDataAsync()
        {
            try
            {
                string serviceCodeText = null;
                string serviceNameText = null;
                string disabilitySupportLevel = null;
                string staffingType = null;
                string searchText = null;

                Dispatcher.Invoke(() =>
                {
                    var serviceCodeTextBox = this.FindName("ServiceCodeTextBox") as TextBox;
                    var serviceNameTextBox = this.FindName("ServiceNameTextBox") as TextBox;
                    var disabilitySupportComboBox = this.FindName("DisabilitySupportComboBox") as ComboBox;
                    var staffingComboBox = this.FindName("StaffingComboBox") as ComboBox;

                    serviceCodeText = serviceCodeTextBox?.Text?.Trim();
                    serviceNameText = serviceNameTextBox?.Text?.Trim();

                    // 获取障害支援区分选择
                    var disabilityItem = disabilitySupportComboBox?.SelectedItem as ComboBoxItem;
                    disabilitySupportLevel = disabilityItem?.Content?.ToString();

                    // 获取人员配置区分选择
                    var staffingItem = staffingComboBox?.SelectedItem as ComboBoxItem;
                    staffingType = staffingItem?.Content?.ToString();

                    //searchText = SearchTextBox?.Text?.Trim();
                });

                string whereClause = "";
                var parameters = new List<SQLiteParameter>();
                var conditions = new List<string>();

                // ServiceCode过滤条件
                if (!string.IsNullOrEmpty(serviceCodeText))
                {
                    conditions.Add(@"""サービスコード"" LIKE @serviceCode");
                    parameters.Add(new SQLiteParameter("@serviceCode", $"%{serviceCodeText}%"));
                }

                // ServiceName过滤条件
                if (!string.IsNullOrEmpty(serviceNameText))
                {
                    conditions.Add(@"""サービス内容略称"" LIKE @serviceName");
                    parameters.Add(new SQLiteParameter("@serviceName", $"%{serviceNameText}%"));
                }

                // 障害支援区分过滤条件
                if (!string.IsNullOrEmpty(disabilitySupportLevel) && disabilitySupportLevel != "全て")
                {
                    conditions.Add(@"""障害支援区分"" LIKE @disabilitySupport");
                    parameters.Add(new SQLiteParameter("@disabilitySupport", $"%{disabilitySupportLevel}%"));
                }

                // 人员配置区分过滤条件（如果有具体选项的话）
                if (!string.IsNullOrEmpty(staffingType) && staffingType != "全て")
                {
                    // 这里可以根据实际的人员配置字段进行过滤
                    // 暂时注释掉，因为不确定具体的字段名
                    // conditions.Add(@"""人員配置区分"" LIKE @staffing");
                    // parameters.Add(new SQLiteParameter("@staffing", $"%{staffingType}%"));
                }

                // 通用搜索条件（严格按照ServiceCodeMaster字段）
                if (!string.IsNullOrEmpty(searchText) && searchText != "サービスコード・サービス内容等のキーワードを入力")
                {
                    conditions.Add(@"""サービスコード"" LIKE @search OR ""サービス内容略称"" LIKE @search OR ""級地コード"" LIKE @search");
                    parameters.Add(new SQLiteParameter("@search", $"%{searchText}%"));
                }

                // 组合所有条件
                if (conditions.Count > 0)
                {
                    whereClause = string.Join(" AND ", conditions);
                }

                // 使用分页查询
                var (serviceCodeMasters, totalCount) = await _serviceCodeRepository.GetPagedAsync(
                    _currentPage, _pageSize, whereClause, parameters.ToArray());

                _totalRecords = totalCount;

                // 转换数据
                var serviceCodeDataList = ConvertServiceCodeMastersToServiceCodeData(serviceCodeMasters);
                _serviceCodeDataList = new ObservableCollection<ServiceCodeData>(serviceCodeDataList);

                // 更新UI（需要在UI线程中执行）
                Application.Current.Dispatcher.Invoke(() =>
                {
                    var serviceCodeDataGrid = this.FindName("ServiceCodeDataGrid") as DataGrid;
                    if (serviceCodeDataGrid != null)
                    {
                        serviceCodeDataGrid.ItemsSource = _serviceCodeDataList;
                    }
                    UpdatePagination();
                    UpdateStatusInfo();
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载分页数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 将ServiceCodeMaster转换为ServiceCodeData
        /// </summary>
        private List<ServiceCodeData> ConvertServiceCodeMastersToServiceCodeData(List<ServiceCodeMaster> serviceCodeMasters)
        {
            var serviceCodeDataList = new List<ServiceCodeData>();

            foreach (var master in serviceCodeMasters)
            {
                var serviceCodeData = new ServiceCodeData();
                serviceCodeData.SetProperty("サービスコード", master.サービスコード);
                serviceCodeData.SetProperty("サービス内容略称", master.サービス内容略称);
                serviceCodeData.SetProperty("障害支援区分", master.障害支援区分);
                serviceCodeData.SetProperty("合成単位", master.合成単位);
                serviceCodeData.SetProperty("級地コード", master.級地コード);
                serviceCodeData.SetProperty("単位数単価", master.単位数単価);
                serviceCodeData.SetProperty("国費単価", master.国費単価);
                serviceCodeData.SetProperty("旧身体療護", master.旧身体療護);
                serviceCodeData.SetProperty("都単価", master.都単価);
                serviceCodeData.SetProperty("キーコード", master.キーコード);
                serviceCodeData.SetProperty("都加算単価", master.都加算単価);
                serviceCodeDataList.Add(serviceCodeData);
            }

            return serviceCodeDataList;
        }



        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            var serviceCodeDataGrid = this.FindName("ServiceCodeDataGrid") as DataGrid;
            if (serviceCodeDataGrid != null)
            {
                // 清除除操作列外的所有列
                var operationColumn = serviceCodeDataGrid.Columns.FirstOrDefault();
                serviceCodeDataGrid.Columns.Clear();
                if (operationColumn != null)
                {
                    serviceCodeDataGrid.Columns.Add(operationColumn);
                }

                // 添加动态列
                foreach (var columnName in _columnNames)
                {
                    var column = new DataGridTextColumn
                    {
                        Header = columnName,
                        Binding = new System.Windows.Data.Binding(columnName),
                        Width = GetColumnWidth(columnName)
                    };
                    serviceCodeDataGrid.Columns.Add(column);
                }
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置合适的宽度
            if (columnName.Contains("服务代码"))
                return new DataGridLength(100);
            else if (columnName.Contains("服务名称"))
                return new DataGridLength(150);
            else if (columnName.Contains("服务分类") || columnName.Contains("单位"))
                return new DataGridLength(100);
            else if (columnName.Contains("基本报酬"))
                return new DataGridLength(80);
            else if (columnName.Contains("地域加算"))
                return new DataGridLength(80);
            else if (columnName.Contains("备注"))
                return new DataGridLength(200);
            else if (columnName.Contains("有效期间"))
                return new DataGridLength(100);
            else
                return new DataGridLength(120);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 获取控件引用
            var paginationControl = this.FindName("PaginationControl") as PaginationControl;
            var serviceCodeDataGrid = this.FindName("ServiceCodeDataGrid") as DataGrid;
            var serviceCodeTextBox = this.FindName("ServiceCodeTextBox") as TextBox;
            var serviceNameTextBox = this.FindName("ServiceNameTextBox") as TextBox;
            var disabilitySupportComboBox = this.FindName("DisabilitySupportComboBox") as ComboBox;
            var staffingComboBox = this.FindName("StaffingComboBox") as ComboBox;

            // 分页控件事件
            if (paginationControl != null)
            {
                paginationControl.PageChanged += PaginationControl_PageChanged;
                paginationControl.PageSizeChanged += PaginationControl_PageSizeChanged;
            }

            // DataGrid选择变化事件
            if (serviceCodeDataGrid != null)
            {
                serviceCodeDataGrid.SelectionChanged += ServiceCodeDataGrid_SelectionChanged;
            }

            // ServiceCode搜索框事件
            if (serviceCodeTextBox != null)
            {
                serviceCodeTextBox.TextChanged += ServiceCodeTextBox_TextChanged;
            }

            // ServiceName搜索框事件
            if (serviceNameTextBox != null)
            {
                serviceNameTextBox.TextChanged += ServiceNameTextBox_TextChanged;
            }

            // 其他过滤控件事件
            if (disabilitySupportComboBox != null)
            {
                disabilitySupportComboBox.SelectionChanged += FilterComboBox_SelectionChanged;
            }

            if (staffingComboBox != null)
            {
                staffingComboBox.SelectionChanged += FilterComboBox_SelectionChanged;
            }

            // 搜索框事件
            //SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            //SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            //SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }



        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
           // SearchTextBox.Foreground = Brushes.Gray;
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            /*if (SearchTextBox.Text == "サービスコード・サービス内容等のキーワードを入力")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }*/
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            /*if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "サービスコード・サービス内容等のキーワードを入力";
                SearchTextBox.Foreground = Brushes.Gray;
            }*/
        }

        /// <summary>
        /// ServiceCode搜索框文本变化事件
        /// </summary>
        private void ServiceCodeTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            _searchTimer?.Stop();
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// ServiceName搜索框文本变化事件
        /// </summary>
        private void ServiceNameTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            _searchTimer?.Stop();
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// 过滤条件ComboBox选择变化事件
        /// </summary>
        private void FilterComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            // 立即应用过滤
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
            }
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void ServiceCodeDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            // 使用数据库分页查询
            Task.Run(async () => await LoadPagedDataAsync());
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var paginationControl = this.FindName("PaginationControl") as PaginationControl;
            if (paginationControl != null)
            {
                var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

                paginationControl.CurrentPage = _currentPage;
                paginationControl.TotalPages = Math.Max(1, totalPages);
                paginationControl.TotalRecords = _totalRecords;
                paginationControl.PageSize = _pageSize;
            }
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            var serviceCodeDataGrid = this.FindName("ServiceCodeDataGrid") as DataGrid;
            var paginationControl = this.FindName("PaginationControl") as PaginationControl;

            int selectedCount = serviceCodeDataGrid?.SelectedItems.Count ?? 0;
            int totalPages = paginationControl?.TotalPages ?? 1;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, totalPages, "");
        }

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new EditWindow("新規サービスコードデータ追加", _columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 创建ServiceCodeMaster对象（严格按照ServiceCodeMaster字段）
                    var serviceCodeMaster = new ServiceCodeMaster
                    {
                        サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                        サービス内容略称 = editWindow.ResultData.GetValueOrDefault("サービス内容略称", "").ToString(),
                        障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                        合成単位 = editWindow.ResultData.GetValueOrDefault("合成単位", "").ToString(),
                        級地コード = editWindow.ResultData.GetValueOrDefault("級地コード", "").ToString(),
                        単位数単価 = editWindow.ResultData.GetValueOrDefault("単位数単価", "").ToString(),
                        国費単価 = editWindow.ResultData.GetValueOrDefault("国費単価", "").ToString(),
                        旧身体療護 = editWindow.ResultData.GetValueOrDefault("旧身体療護", "").ToString(),
                        都単価 = editWindow.ResultData.GetValueOrDefault("都単価", "").ToString(),
                        キーコード = editWindow.ResultData.GetValueOrDefault("キーコード", "").ToString(),
                        都加算単価 = editWindow.ResultData.GetValueOrDefault("都加算単価", "").ToString()
                    };

                    // 保存到数据库
                    await _serviceCodeRepository.InsertAsync(serviceCodeMaster);

                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("データの追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as ServiceCodeData;
            if (selectedItem == null)
            {
                MessageBox.Show("編集するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editData = new Dictionary<string, object>();
                foreach (var columnName in _columnNames)
                {
                    editData[columnName] = selectedItem.GetProperty(columnName);
                }
                var editWindow = new EditWindow("サービスコードデータ編集", _columnNames, editData);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    // 获取原始サービスコード用于更新
                    var originalServiceCode = selectedItem["サービスコード"]?.ToString();
                    if (!string.IsNullOrEmpty(originalServiceCode))
                    {
                        // 创建更新的ServiceCodeMaster对象（严格按照ServiceCodeMaster字段）
                        var serviceCodeMaster = new ServiceCodeMaster
                        {
                            サービスコード = editWindow.ResultData.GetValueOrDefault("サービスコード", "").ToString(),
                            サービス内容略称 = editWindow.ResultData.GetValueOrDefault("サービス内容略称", "").ToString(),
                            障害支援区分 = editWindow.ResultData.GetValueOrDefault("障害支援区分", "").ToString(),
                            合成単位 = editWindow.ResultData.GetValueOrDefault("合成単位", "").ToString(),
                            級地コード = editWindow.ResultData.GetValueOrDefault("級地コード", "").ToString(),
                            単位数単価 = editWindow.ResultData.GetValueOrDefault("単位数単価", "").ToString(),
                            国費単価 = editWindow.ResultData.GetValueOrDefault("国費単価", "").ToString(),
                            旧身体療護 = editWindow.ResultData.GetValueOrDefault("旧身体療護", "").ToString(),
                            都単価 = editWindow.ResultData.GetValueOrDefault("都単価", "").ToString(),
                            キーコード = editWindow.ResultData.GetValueOrDefault("キーコード", "").ToString(),
                            都加算単価 = editWindow.ResultData.GetValueOrDefault("都加算単価", "").ToString()
                        };

                        // 更新数据库（使用原始サービスコード作为条件）
                        await _serviceCodeRepository.UpdateAsync(serviceCodeMaster, "\"サービスコード\" = @originalServiceCode",
                            _serviceCodeRepository.CreateParameter("@originalServiceCode", originalServiceCode));

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("サービスコードを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var button = sender as Button;
            var selectedItem = button?.Tag as ServiceCodeData;
            if (selectedItem == null)
            {
                MessageBox.Show("削除するレコードを選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var serviceCode = selectedItem["サービスコード"]?.ToString() ?? "不明";
            var result = MessageBox.Show($"サービスコード「{serviceCode}」のデータを削除しますか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    // 获取サービスコード用于删除
                    var serviceCodeValue = selectedItem["サービスコード"]?.ToString();
                    if (!string.IsNullOrEmpty(serviceCodeValue))
                    {
                        // 从数据库删除（使用サービスコード作为主键）
                        await _serviceCodeRepository.DeleteAsync("\"サービスコード\" = @serviceCode",
                            _serviceCodeRepository.CreateParameter("@serviceCode", serviceCodeValue));

                        // 刷新数据
                        LoadDataFromDatabase();
                        MessageBox.Show("データの削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("サービスコードを取得できません！", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"データ削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// CSV导入按钮点击事件
        /// </summary>
        private async void ImportCsvButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new Microsoft.Win32.OpenFileDialog
                {
                    Title = "CSVファイルを選択してください",
                    Filter = "CSVファイル (*.csv)|*.csv|すべてのファイル (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var csvImportService = new ServiceCodeCsvImportService();

                    // 显示进度提示
                    var progressWindow = new ProgressWindow("CSVデータを取込中...");
                    progressWindow.Show();

                    try
                    {
                        var result = await csvImportService.ImportServiceCodeCsvAsync(openFileDialog.FileName);

                        progressWindow.Close();

                        if (result.IsSuccess)
                        {
                            LoadDataFromDatabase();
                            MessageBox.Show($"CSV取込が完了しました！\n{result.SuccessMessage}", "取込成功",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            var errorMessage = result.ErrorMessage;
                            if (result.ValidationErrors.Any())
                            {
                                errorMessage += "\n\n詳細エラー：\n" + string.Join("\n", result.ValidationErrors);
                            }
                            MessageBox.Show(errorMessage, "取込失敗", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    catch (Exception importEx)
                    {
                        progressWindow.Close();
                        MessageBox.Show($"CSV取込中にエラーが発生しました：{importEx.Message}", "エラー",
                            MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                    finally
                    {
                        csvImportService?.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"CSVインポートに失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// CSV导出按钮点击事件
        /// </summary>
        private void ExportCsvButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new Microsoft.Win32.SaveFileDialog
                {
                    Title = "CSVファイルを保存",
                    Filter = "CSVファイル (*.csv)|*.csv|すべてのファイル (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true,
                    FileName = $"ServiceCodeData_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // TODO: 实现CSV导出功能
                    MessageBox.Show($"CSVエクスポート機能は開発中です。\n保存先: {saveFileDialog.FileName}",
                        "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"CSVエクスポートに失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
