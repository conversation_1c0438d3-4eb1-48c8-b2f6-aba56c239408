using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 数据访问基类
    /// </summary>
    /// <typeparam name="T">实体类型</typeparam>
    public abstract class BaseRepository<T> where T : class, new()
    {
        protected readonly DatabaseService _databaseService;
        protected readonly string _tableName;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databaseService">数据库服务</param>
        /// <param name="tableName">表名</param>
        protected BaseRepository(DatabaseService databaseService, string tableName)
        {
            _databaseService = databaseService ?? throw new ArgumentNullException(nameof(databaseService));
            _tableName = tableName ?? throw new ArgumentNullException(nameof(tableName));
        }

        /// <summary>
        /// 获取所有记录
        /// </summary>
        public virtual async Task<List<T>> GetAllAsync()
        {
            try
            {
                var sql = $"SELECT * FROM {_tableName}";
                var dataTable = await _databaseService.ExecuteQueryAsync(sql);
                return ConvertDataTableToList(dataTable);
            }
            catch (Exception ex)
            {
                throw new Exception($"获取所有{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据条件查询记录
        /// </summary>
        public virtual async Task<List<T>> GetByConditionAsync(string whereClause, params SQLiteParameter[] parameters)
        {
            try
            {
                var sql = $"SELECT * FROM {_tableName} WHERE {whereClause}";
                var dataTable = await _databaseService.ExecuteQueryAsync(sql, parameters);
                return ConvertDataTableToList(dataTable);
            }
            catch (Exception ex)
            {
                throw new Exception($"根据条件查询{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 分页查询
        /// </summary>
        public virtual async Task<(List<T> items, int totalCount)> GetPagedAsync(int pageIndex, int pageSize, string whereClause = "", params SQLiteParameter[] parameters)
        {
            try
            {
                // 获取总记录数
                var countSql = string.IsNullOrEmpty(whereClause) 
                    ? $"SELECT COUNT(*) FROM {_tableName}"
                    : $"SELECT COUNT(*) FROM {_tableName} WHERE {whereClause}";
                
                var totalCount = Convert.ToInt32(await _databaseService.ExecuteScalarAsync(countSql, parameters));

                // 获取分页数据
                var dataSql = string.IsNullOrEmpty(whereClause)
                    ? $"SELECT * FROM {_tableName} LIMIT {pageSize} OFFSET {(pageIndex - 1) * pageSize}"
                    : $"SELECT * FROM {_tableName} WHERE {whereClause} LIMIT {pageSize} OFFSET {(pageIndex - 1) * pageSize}";

                var dataTable = await _databaseService.ExecuteQueryAsync(dataSql, parameters);
                var items = ConvertDataTableToList(dataTable);

                return (items, totalCount);
            }
            catch (Exception ex)
            {
                throw new Exception($"分页查询{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 插入记录
        /// </summary>
        public virtual async Task<int> InsertAsync(T entity)
        {
            try
            {
                var (sql, parameters) = BuildInsertCommand(entity);
                return await _databaseService.ExecuteNonQueryAsync(sql, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"插入{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量插入记录
        /// </summary>
        public virtual async Task<int> BulkInsertAsync(List<T> entities)
        {
            try
            {
                var dataTable = ConvertListToDataTable(entities);
                return await _databaseService.BulkInsertAsync(_tableName, dataTable);
            }
            catch (Exception ex)
            {
                throw new Exception($"批量插入{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新记录
        /// </summary>
        public virtual async Task<int> UpdateAsync(T entity, string whereClause, params SQLiteParameter[] whereParameters)
        {
            try
            {
                var (sql, parameters) = BuildUpdateCommand(entity, whereClause, whereParameters);
                return await _databaseService.ExecuteNonQueryAsync(sql, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"更新{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除记录
        /// </summary>
        public virtual async Task<int> DeleteAsync(string whereClause, params SQLiteParameter[] parameters)
        {
            try
            {
                var sql = $"DELETE FROM {_tableName} WHERE {whereClause}";
                return await _databaseService.ExecuteNonQueryAsync(sql, parameters);
            }
            catch (Exception ex)
            {
                throw new Exception($"删除{_tableName}记录失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 清空表
        /// </summary>
        public virtual async Task<int> TruncateAsync()
        {
            try
            {
                var sql = $"DELETE FROM {_tableName}";
                return await _databaseService.ExecuteNonQueryAsync(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"清空{_tableName}表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查记录是否存在
        /// </summary>
        public virtual async Task<bool> ExistsAsync(string whereClause, params SQLiteParameter[] parameters)
        {
            try
            {
                var sql = $"SELECT COUNT(*) FROM {_tableName} WHERE {whereClause}";
                var count = Convert.ToInt32(await _databaseService.ExecuteScalarAsync(sql, parameters));
                return count > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"检查{_tableName}记录存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 将DataTable转换为实体列表（需要子类实现）
        /// </summary>
        protected abstract List<T> ConvertDataTableToList(DataTable dataTable);

        /// <summary>
        /// 将实体列表转换为DataTable（需要子类实现）
        /// </summary>
        protected abstract DataTable ConvertListToDataTable(List<T> entities);

        /// <summary>
        /// 构建插入命令（需要子类实现）
        /// </summary>
        protected abstract (string sql, SQLiteParameter[] parameters) BuildInsertCommand(T entity);

        /// <summary>
        /// 构建更新命令（需要子类实现）
        /// </summary>
        protected abstract (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(T entity, string whereClause, SQLiteParameter[] whereParameters);

        /// <summary>
        /// 获取列名列表（需要子类实现）
        /// </summary>
        protected abstract List<string> GetColumnNames();

        /// <summary>
        /// 辅助方法：安全获取DataRow中的字符串值
        /// </summary>
        protected string GetStringValue(DataRow row, string columnName)
        {
            return row[columnName]?.ToString() ?? string.Empty;
        }

        /// <summary>
        /// 辅助方法：安全获取DataRow中的整数值
        /// </summary>
        protected int GetIntValue(DataRow row, string columnName)
        {
            var value = row[columnName];
            if (value == null || value == DBNull.Value) return 0;
            return Convert.ToInt32(value);
        }

        /// <summary>
        /// 辅助方法：安全获取DataRow中的浮点数值
        /// </summary>
        protected double GetDoubleValue(DataRow row, string columnName)
        {
            var value = row[columnName];
            if (value == null || value == DBNull.Value) return 0.0;
            return Convert.ToDouble(value);
        }

        /// <summary>
        /// 辅助方法：安全获取DataRow中的日期时间值
        /// </summary>
        protected DateTime? GetDateTimeValue(DataRow row, string columnName)
        {
            var value = row[columnName];
            if (value == null || value == DBNull.Value) return null;
            if (DateTime.TryParse(value.ToString(), out DateTime result))
                return result;
            return null;
        }

        /// <summary>
        /// 辅助方法：创建SQLiteParameter
        /// </summary>
        public SQLiteParameter CreateParameter(string name, object value)
        {
            return new SQLiteParameter(name, value ?? DBNull.Value);
        }
    }
}
