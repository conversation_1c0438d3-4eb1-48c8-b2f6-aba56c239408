using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services
{
    /// <summary>
    /// SQLite数据库服务类
    /// </summary>
    public class DatabaseService : IDisposable
    {
        private readonly string _connectionString;
        private SQLiteConnection _connection;
        private bool _disposed = false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="databasePath">数据库文件路径</param>
        public DatabaseService(string databasePath = null)
        {
            if (string.IsNullOrEmpty(databasePath))
            {
                var dbDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database");
                Directory.CreateDirectory(dbDirectory);
                databasePath = Path.Combine(dbDirectory, "navi.db");
            }

            _connectionString = $"Data Source={databasePath}";
            InitializeDatabase();
        }

        /// <summary>
        /// 获取数据库连接
        /// </summary>
        private SQLiteConnection GetConnection()
        {
            if (_connection == null || _connection.State != ConnectionState.Open)
            {
                _connection?.Dispose();
                _connection = new SQLiteConnection(_connectionString);
                _connection.Open();
            }
            return _connection;
        }

        /// <summary>
        /// 初始化数据库（创建表结构）
        /// </summary>
        private void InitializeDatabase()
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    connection.Open();

                    // 读取数据库结构脚本
                    /*var scriptPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "database", "database.txt");
                    if (File.Exists(scriptPath))
                    {
                        var script = File.ReadAllText(scriptPath);
                        var statements = script.Split(new[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

                        foreach (var statement in statements)
                        {
                            var trimmedStatement = statement.Trim();
                            if (!string.IsNullOrEmpty(trimmedStatement))
                            {
                                using (var command = new SQLiteCommand(trimmedStatement, connection))
                                {
                                    command.ExecuteNonQuery();
                                }
                            }
                        }
                    }
                    else
                    {
                        // 如果脚本文件不存在，手动创建表结构
                        CreateTablesManually(connection);
                    }*/
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"数据库初始化失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 手动创建表结构
        /// </summary>
        private void CreateTablesManually(SQLiteConnection connection)
        {
            var createTableCommands = new[]
            {
                @"CREATE TABLE IF NOT EXISTS ServiceCodeMaster (
                    ""サービスコード"" TEXT,
                    ""サービス内容略称"" TEXT,
                    ""障害支援区分"" TEXT,
                    ""合成単位"" TEXT,
                    ""級地コード"" TEXT,
                    ""単位数単価"" TEXT,
                    ""国費単価"" TEXT,
                    ""旧身体療護"" TEXT,
                    ""都単価"" TEXT,
                    ""キーコード"" TEXT,
                    ""都加算単価"" TEXT
                )",

                @"CREATE TABLE IF NOT EXISTS ServiceProviders (
                    ""No"" INTEGER PRIMARY KEY AUTOINCREMENT,
                    ""事業者番号"" TEXT,
                    ""郵便番号"" TEXT,
                    ""所在地"" TEXT,
                    ""事業者名称"" TEXT,
                    ""代表者役職"" TEXT,
                    ""代表者名"" TEXT,
                    ""担当者氏名"" TEXT,
                    ""連絡先"" TEXT,
                    ""サービス種別"" TEXT,
                    ""加算対象サービス"" TEXT,
                    ""第三者評価結果"" TEXT,
                    ""研修受講証明"" TEXT,
                    ""利用者情報"" TEXT
                )",

                @"CREATE TABLE IF NOT EXISTS KokuhoRenData (
                    ""No"" INTEGER PRIMARY KEY AUTOINCREMENT,
                    ""サービス提供年月"" TEXT,
                    ""請求年月日"" TEXT,
                    ""請求回数"" TEXT,
                    ""審査年月"" TEXT,
                    ""事業者コード"" TEXT,
                    ""事業者名称"" TEXT,
                    ""受給者番号"" TEXT,
                    ""受給者名称"" TEXT,
                    ""受給者名称カナ"" TEXT,
                    ""児童名称"" TEXT,
                    ""児童名称カナ"" TEXT,
                    ""身体"" TEXT,
                    ""知的"" TEXT,
                    ""精神"" TEXT,
                    ""難病"" TEXT,
                    ""単価障害程度区分"" TEXT,
                    ""障害支援区分"" TEXT,
                    ""サービスコード"" TEXT,
                    ""サービス名称"" TEXT,
                    ""算定時間"" TEXT,
                    ""回数"" TEXT,
                    ""算定時間x回数"" TEXT,
                    ""単位数"" TEXT,
                    ""サービス単位"" TEXT,
                    ""連合会審査区分名称"" TEXT,
                    ""審査区分名称"" TEXT,
                    ""返戻事由名称"" TEXT,
                    ""判定フラグ"" TEXT,
                    ""status"" TEXT
                )",

                @"CREATE TABLE IF NOT EXISTS RecipientsData (
                    ""No"" INTEGER PRIMARY KEY AUTOINCREMENT,
                    ""登録日"" TEXT,
                    ""事業者番号"" TEXT,
                    ""事業者郵便番号"" TEXT,
                    ""事業者住所"" TEXT,
                    ""事業者名称"" TEXT,
                    ""代表者名"" TEXT,
                    ""代表者役職"" TEXT,
                    ""サービス提供年月"" TEXT,
                    ""明細書件数"" INTEGER,
                    ""請求金額"" REAL,
                    ""第三者評価"" TEXT,
                    ""受給者番号"" TEXT,
                    ""支給決定障害者氏名"" TEXT,
                    ""支給決定に係る障害児氏名"" TEXT,
                    ""障害支援区分"" TEXT,
                    ""事業者名称2"" TEXT,
                    ""地域区分"" TEXT,
                    ""旧身体療護施設区分"" TEXT,
                    ""精神科医療連携体制加算"" TEXT,
                    ""開始年月日"" TEXT,
                    ""終了年月日"" TEXT,
                    ""利用日数全体"" INTEGER,
                    ""サービスコード"" TEXT,
                    ""サービス内容"" TEXT,
                    ""算定単価額"" REAL,
                    ""利用日数"" INTEGER,
                    ""当月算定額"" REAL,
                    ""摘要"" TEXT,
                    ""status"" TEXT
                )",

                @"CREATE TABLE IF NOT EXISTS Users (
                    ""職員番号"" TEXT PRIMARY KEY,
                    ""部署名"" TEXT,
                    ""役職"" TEXT,
                    ""氏名"" TEXT,
                    ""ID番号"" TEXT,
                    ""パスワード"" TEXT,
                    ""作成日時"" TEXT,
                    ""更新日時"" TEXT
                )"
            };

            foreach (var command in createTableCommands)
            {
                using (var cmd = new SQLiteCommand(command, connection))
                {
                    cmd.ExecuteNonQuery();
                }
            }

            // 插入默认管理员用户
            var insertAdminUser = @"INSERT OR IGNORE INTO Users
                (""職員番号"", ""部署名"", ""役職"", ""氏名"", ""ID番号"", ""パスワード"", ""作成日時"", ""更新日時"")
                VALUES ('admin', 'システム管理部', '管理者', '管理者', '2025011741', '123456', datetime('now'), datetime('now'))";

            using (var adminCmd = new SQLiteCommand(insertAdminUser, connection))
            {
                adminCmd.ExecuteNonQuery();
            }
        }

        /// <summary>
        /// 执行查询并返回DataTable
        /// </summary>
        public async Task<DataTable> ExecuteQueryAsync(string sql, params SQLiteParameter[] parameters)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        using (var reader = await command.ExecuteReaderAsync())
                        {
                            var dataTable = new DataTable();
                            dataTable.Load(reader);
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"查询执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行非查询命令（INSERT, UPDATE, DELETE）
        /// </summary>
        public async Task<int> ExecuteNonQueryAsync(string sql, params SQLiteParameter[] parameters)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        return await command.ExecuteNonQueryAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"命令执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 执行标量查询（返回单个值）
        /// </summary>
        public async Task<object> ExecuteScalarAsync(string sql, params SQLiteParameter[] parameters)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        return await command.ExecuteScalarAsync();
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"标量查询执行失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量插入数据
        /// </summary>
        public async Task<int> BulkInsertAsync(string tableName, DataTable dataTable)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        var insertedCount = 0;
                        var columns = new List<string>();
                        foreach (DataColumn column in dataTable.Columns)
                        {
                            columns.Add($"\"{column.ColumnName}\"");
                        }

                        var parameterNames = columns.Select(c => "@" + c.Replace("\"", "").Replace(" ", "_").Replace("×", "x")).ToList();
                        var sql = $"INSERT INTO {tableName} ({string.Join(", ", columns)}) VALUES ({string.Join(", ", parameterNames)})";

                        using (var command = new SQLiteCommand(sql, connection, transaction))
                        {
                            foreach (DataRow row in dataTable.Rows)
                            {
                                command.Parameters.Clear();
                                for (int i = 0; i < dataTable.Columns.Count; i++)
                                {
                                    var paramName = parameterNames[i];
                                    command.Parameters.AddWithValue(paramName, row[i] ?? DBNull.Value);
                                }

                                await command.ExecuteNonQueryAsync();
                                insertedCount++;
                            }
                        }

                        transaction.Commit();
                        return insertedCount;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"批量插入失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 分页查询数据
        /// </summary>
        public async Task<(DataTable data, int totalCount)> GetPagedDataAsync(string tableName, int pageIndex, int pageSize, string whereClause = "", params SQLiteParameter[] parameters)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    // 获取总记录数
                    var countSql = string.IsNullOrEmpty(whereClause)
                        ? $"SELECT COUNT(*) FROM {tableName}"
                        : $"SELECT COUNT(*) FROM {tableName} WHERE {whereClause}";

                    int totalCount;
                    using (var countCommand = new SQLiteCommand(countSql, connection))
                    {
                        if (parameters != null && parameters.Length > 0)
                        {
                            countCommand.Parameters.AddRange(parameters);
                        }
                        totalCount = Convert.ToInt32(await countCommand.ExecuteScalarAsync());
                    }

                    // 获取分页数据
                    var dataSql = string.IsNullOrEmpty(whereClause)
                        ? $"SELECT * FROM {tableName} LIMIT {pageSize} OFFSET {(pageIndex - 1) * pageSize}"
                        : $"SELECT * FROM {tableName} WHERE {whereClause} LIMIT {pageSize} OFFSET {(pageIndex - 1) * pageSize}";

                    DataTable dataTable;
                    using (var dataCommand = new SQLiteCommand(dataSql, connection))
                    {
                        if (parameters != null && parameters.Length > 0)
                        {
                            dataCommand.Parameters.AddRange(parameters);
                        }

                        using (var reader = await dataCommand.ExecuteReaderAsync())
                        {
                            dataTable = new DataTable();
                            dataTable.Load(reader);
                        }
                    }

                    return (dataTable, totalCount);
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"分页查询失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 插入单条记录并返回插入的ID
        /// </summary>
        public async Task<long> InsertAndGetIdAsync(string sql, params SQLiteParameter[] parameters)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var command = new SQLiteCommand(sql, connection))
                    {
                        if (parameters != null)
                        {
                            command.Parameters.AddRange(parameters);
                        }

                        await command.ExecuteNonQueryAsync();

                        // 获取最后插入的ID
                        using (var idCommand = new SQLiteCommand("SELECT last_insert_rowid()", connection))
                        {
                            return Convert.ToInt64(await idCommand.ExecuteScalarAsync());
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"插入记录并获取ID失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量更新数据
        /// </summary>
        public async Task<int> BatchUpdateAsync(string tableName, List<Dictionary<string, object>> updates, string keyColumn)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        var updatedCount = 0;

                        foreach (var update in updates)
                        {
                            if (!update.ContainsKey(keyColumn))
                                continue;

                            var setParts = new List<string>();
                            var parameters = new List<SQLiteParameter>();

                            foreach (var kvp in update)
                            {
                                if (kvp.Key != keyColumn)
                                {
                                    setParts.Add($"\"{kvp.Key}\" = @{kvp.Key}");
                                    parameters.Add(new SQLiteParameter($"@{kvp.Key}", kvp.Value ?? DBNull.Value));
                                }
                            }

                            if (setParts.Count > 0)
                            {
                                var sql = $"UPDATE {tableName} SET {string.Join(", ", setParts)} WHERE \"{keyColumn}\" = @{keyColumn}";
                                parameters.Add(new SQLiteParameter($"@{keyColumn}", update[keyColumn]));

                                using (var command = new SQLiteCommand(sql, connection, transaction))
                                {
                                    command.Parameters.AddRange(parameters.ToArray());
                                    updatedCount += await command.ExecuteNonQueryAsync();
                                }
                            }
                        }

                        transaction.Commit();
                        return updatedCount;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"批量更新失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量删除数据
        /// </summary>
        public async Task<int> BatchDeleteAsync(string tableName, string keyColumn, List<object> keyValues)
        {
            try
            {
                using (var connection = new SQLiteConnection(_connectionString))
                {
                    await connection.OpenAsync();

                    using (var transaction = connection.BeginTransaction())
                    {
                        var deletedCount = 0;

                        foreach (var keyValue in keyValues)
                        {
                            var sql = $"DELETE FROM {tableName} WHERE \"{keyColumn}\" = @keyValue";
                            using (var command = new SQLiteCommand(sql, connection, transaction))
                            {
                                command.Parameters.AddWithValue("@keyValue", keyValue);
                                deletedCount += await command.ExecuteNonQueryAsync();
                            }
                        }

                        transaction.Commit();
                        return deletedCount;
                    }
                }
            }
            catch (Exception ex)
            {
                throw new Exception($"批量删除失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查记录是否存在
        /// </summary>
        public async Task<bool> RecordExistsAsync(string tableName, string whereClause, params SQLiteParameter[] parameters)
        {
            try
            {
                var sql = $"SELECT COUNT(*) FROM {tableName} WHERE {whereClause}";
                var count = Convert.ToInt32(await ExecuteScalarAsync(sql, parameters));
                return count > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"检查记录存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 获取表的记录总数
        /// </summary>
        public async Task<int> GetRecordCountAsync(string tableName, string whereClause = "", params SQLiteParameter[] parameters)
        {
            try
            {
                var sql = string.IsNullOrEmpty(whereClause)
                    ? $"SELECT COUNT(*) FROM {tableName}"
                    : $"SELECT COUNT(*) FROM {tableName} WHERE {whereClause}";

                return Convert.ToInt32(await ExecuteScalarAsync(sql, parameters));
            }
            catch (Exception ex)
            {
                throw new Exception($"获取记录总数失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 清空表数据
        /// </summary>
        public async Task<int> TruncateTableAsync(string tableName)
        {
            try
            {
                var sql = $"DELETE FROM {tableName}";
                return await ExecuteNonQueryAsync(sql);
            }
            catch (Exception ex)
            {
                throw new Exception($"清空表失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            Dispose(true);
            GC.SuppressFinalize(this);
        }

        protected virtual void Dispose(bool disposing)
        {
            if (!_disposed)
            {
                if (disposing)
                {
                    _connection?.Dispose();
                }
                _disposed = true;
            }
        }
    }
}
