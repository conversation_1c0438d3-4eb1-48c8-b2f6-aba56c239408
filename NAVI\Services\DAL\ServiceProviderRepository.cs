using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 服务提供者实体类
    /// </summary>
    public class ServiceProvider
    {
        public int No { get; set; }
        public string 事業者番号 { get; set; } = string.Empty;
        public string 郵便番号 { get; set; } = string.Empty;
        public string 所在地 { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 代表者役職 { get; set; } = string.Empty;
        public string 代表者名 { get; set; } = string.Empty;
        public string 担当者氏名 { get; set; } = string.Empty;
        public string 連絡先 { get; set; } = string.Empty;
        public string サービス種別 { get; set; } = string.Empty;
        public string 加算対象サービス { get; set; } = string.Empty;
        public string 第三者評価結果 { get; set; } = string.Empty;
        public string 研修受講証明 { get; set; } = string.Empty;
        public string 利用者情報 { get; set; } = string.Empty;
    }

    /// <summary>
    /// 服务提供者数据访问类
    /// </summary>
    public class ServiceProviderRepository : BaseRepository<ServiceProvider>
    {
        public ServiceProviderRepository(DatabaseService databaseService) 
            : base(databaseService, "ServiceProviders")
        {
        }

        /// <summary>
        /// 根据事业者番号获取服务提供者
        /// </summary>
        public async Task<ServiceProvider> GetByProviderNumberAsync(string providerNumber)
        {
            try
            {
                var providers = await GetByConditionAsync("\"事業者番号\" = @providerNumber",
                    CreateParameter("@providerNumber", providerNumber));
                return providers.FirstOrDefault();
            }
            catch (Exception ex)
            {
                throw new Exception($"根据事业者番号获取服务提供者失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据事业者名称搜索服务提供者
        /// </summary>
        public async Task<List<ServiceProvider>> SearchByNameAsync(string name)
        {
            try
            {
                return await GetByConditionAsync("\"事業者名称\" LIKE @name",
                    CreateParameter("@name", $"%{name}%"));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据事业者名称搜索服务提供者失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据服务种别获取服务提供者
        /// </summary>
        public async Task<List<ServiceProvider>> GetByServiceTypeAsync(string serviceType)
        {
            try
            {
                return await GetByConditionAsync("\"サービス種別\" = @serviceType",
                    CreateParameter("@serviceType", serviceType));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据服务种别获取服务提供者失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 检查事业者番号是否存在
        /// </summary>
        public async Task<bool> ProviderNumberExistsAsync(string providerNumber)
        {
            try
            {
                return await ExistsAsync("\"事業者番号\" = @providerNumber",
                    CreateParameter("@providerNumber", providerNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"检查事业者番号存在性失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 创建新的服务提供者
        /// </summary>
        public async Task<int> CreateProviderAsync(ServiceProvider provider)
        {
            try
            {
                return await InsertAsync(provider);
            }
            catch (Exception ex)
            {
                throw new Exception($"创建服务提供者失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新服务提供者信息
        /// </summary>
        public async Task<int> UpdateProviderAsync(ServiceProvider provider)
        {
            try
            {
                return await UpdateAsync(provider, "\"No\" = @no", CreateParameter("@no", provider.No));
            }
            catch (Exception ex)
            {
                throw new Exception($"更新服务提供者失败: {ex.Message}", ex);
            }
        }

        protected override List<ServiceProvider> ConvertDataTableToList(DataTable dataTable)
        {
            var providers = new List<ServiceProvider>();
            foreach (DataRow row in dataTable.Rows)
            {
                providers.Add(new ServiceProvider
                {
                    No = GetIntValue(row, "No"),
                    事業者番号 = GetStringValue(row, "事業者番号"),
                    郵便番号 = GetStringValue(row, "郵便番号"),
                    所在地 = GetStringValue(row, "所在地"),
                    事業者名称 = GetStringValue(row, "事業者名称"),
                    代表者役職 = GetStringValue(row, "代表者役職"),
                    代表者名 = GetStringValue(row, "代表者名"),
                    担当者氏名 = GetStringValue(row, "担当者氏名"),
                    連絡先 = GetStringValue(row, "連絡先"),
                    サービス種別 = GetStringValue(row, "サービス種別"),
                    加算対象サービス = GetStringValue(row, "加算対象サービス"),
                    第三者評価結果 = GetStringValue(row, "第三者評価結果"),
                    研修受講証明 = GetStringValue(row, "研修受講証明"),
                    利用者情報 = GetStringValue(row, "利用者情報")
                });
            }
            return providers;
        }

        protected override DataTable ConvertListToDataTable(List<ServiceProvider> entities)
        {
            var dataTable = new DataTable();
            var columns = GetColumnNames();
            
            foreach (var column in columns)
            {
                dataTable.Columns.Add(column);
            }

            foreach (var provider in entities)
            {
                var row = dataTable.NewRow();
                row["No"] = provider.No;
                row["事業者番号"] = provider.事業者番号;
                row["郵便番号"] = provider.郵便番号;
                row["所在地"] = provider.所在地;
                row["事業者名称"] = provider.事業者名称;
                row["代表者役職"] = provider.代表者役職;
                row["代表者名"] = provider.代表者名;
                row["担当者氏名"] = provider.担当者氏名;
                row["連絡先"] = provider.連絡先;
                row["サービス種別"] = provider.サービス種別;
                row["加算対象サービス"] = provider.加算対象サービス;
                row["第三者評価結果"] = provider.第三者評価結果;
                row["研修受講証明"] = provider.研修受講証明;
                row["利用者情報"] = provider.利用者情報;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildInsertCommand(ServiceProvider entity)
        {
            var sql = @"INSERT INTO ServiceProviders 
                (""事業者番号"", ""郵便番号"", ""所在地"", ""事業者名称"", ""代表者役職"", ""代表者名"", ""担当者氏名"", ""連絡先"", ""サービス種別"", ""加算対象サービス"", ""第三者評価結果"", ""研修受講証明"", ""利用者情報"") 
                VALUES (@事業者番号, @郵便番号, @所在地, @事業者名称, @代表者役職, @代表者名, @担当者氏名, @連絡先, @サービス種別, @加算対象サービス, @第三者評価結果, @研修受講証明, @利用者情報)";

            var parameters = new[]
            {
                CreateParameter("@事業者番号", entity.事業者番号),
                CreateParameter("@郵便番号", entity.郵便番号),
                CreateParameter("@所在地", entity.所在地),
                CreateParameter("@事業者名称", entity.事業者名称),
                CreateParameter("@代表者役職", entity.代表者役職),
                CreateParameter("@代表者名", entity.代表者名),
                CreateParameter("@担当者氏名", entity.担当者氏名),
                CreateParameter("@連絡先", entity.連絡先),
                CreateParameter("@サービス種別", entity.サービス種別),
                CreateParameter("@加算対象サービス", entity.加算対象サービス),
                CreateParameter("@第三者評価結果", entity.第三者評価結果),
                CreateParameter("@研修受講証明", entity.研修受講証明),
                CreateParameter("@利用者情報", entity.利用者情報)
            };

            return (sql, parameters);
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(ServiceProvider entity, string whereClause, SQLiteParameter[] whereParameters)
        {
            var setParts = new List<string>();
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(entity.事業者番号))
            {
                setParts.Add("\"事業者番号\" = @事業者番号");
                parameters.Add(CreateParameter("@事業者番号", entity.事業者番号));
            }
            if (!string.IsNullOrEmpty(entity.郵便番号))
            {
                setParts.Add("\"郵便番号\" = @郵便番号");
                parameters.Add(CreateParameter("@郵便番号", entity.郵便番号));
            }
            if (!string.IsNullOrEmpty(entity.所在地))
            {
                setParts.Add("\"所在地\" = @所在地");
                parameters.Add(CreateParameter("@所在地", entity.所在地));
            }
            if (!string.IsNullOrEmpty(entity.事業者名称))
            {
                setParts.Add("\"事業者名称\" = @事業者名称");
                parameters.Add(CreateParameter("@事業者名称", entity.事業者名称));
            }
            if (!string.IsNullOrEmpty(entity.代表者役職))
            {
                setParts.Add("\"代表者役職\" = @代表者役職");
                parameters.Add(CreateParameter("@代表者役職", entity.代表者役職));
            }
            if (!string.IsNullOrEmpty(entity.代表者名))
            {
                setParts.Add("\"代表者名\" = @代表者名");
                parameters.Add(CreateParameter("@代表者名", entity.代表者名));
            }
            if (!string.IsNullOrEmpty(entity.担当者氏名))
            {
                setParts.Add("\"担当者氏名\" = @担当者氏名");
                parameters.Add(CreateParameter("@担当者氏名", entity.担当者氏名));
            }
            if (!string.IsNullOrEmpty(entity.連絡先))
            {
                setParts.Add("\"連絡先\" = @連絡先");
                parameters.Add(CreateParameter("@連絡先", entity.連絡先));
            }
            if (!string.IsNullOrEmpty(entity.サービス種別))
            {
                setParts.Add("\"サービス種別\" = @サービス種別");
                parameters.Add(CreateParameter("@サービス種別", entity.サービス種別));
            }
            if (!string.IsNullOrEmpty(entity.加算対象サービス))
            {
                setParts.Add("\"加算対象サービス\" = @加算対象サービス");
                parameters.Add(CreateParameter("@加算対象サービス", entity.加算対象サービス));
            }
            if (!string.IsNullOrEmpty(entity.第三者評価結果))
            {
                setParts.Add("\"第三者評価結果\" = @第三者評価結果");
                parameters.Add(CreateParameter("@第三者評価結果", entity.第三者評価結果));
            }
            if (!string.IsNullOrEmpty(entity.研修受講証明))
            {
                setParts.Add("\"研修受講証明\" = @研修受講証明");
                parameters.Add(CreateParameter("@研修受講証明", entity.研修受講証明));
            }
            if (!string.IsNullOrEmpty(entity.利用者情報))
            {
                setParts.Add("\"利用者情報\" = @利用者情報");
                parameters.Add(CreateParameter("@利用者情報", entity.利用者情報));
            }

            if (whereParameters != null)
            {
                parameters.AddRange(whereParameters);
            }

            var sql = $"UPDATE ServiceProviders SET {string.Join(", ", setParts)} WHERE {whereClause}";
            return (sql, parameters.ToArray());
        }

        protected override List<string> GetColumnNames()
        {
            return new List<string>
            {
                "No", "事業者番号", "郵便番号", "所在地", "事業者名称", "代表者役職", "代表者名", "担当者氏名", "連絡先", "サービス種別", "加算対象サービス", "第三者評価結果", "研修受講証明", "利用者情報"
            };
        }
    }
}
