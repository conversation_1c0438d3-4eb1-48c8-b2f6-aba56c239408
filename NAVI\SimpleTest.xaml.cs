using System;
using System.Windows;

namespace NAVI
{
    public partial class SimpleTest : Window
    {
        public SimpleTest()
        {
            InitializeComponent();
        }

        private void GetValue_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var value = TestPicker.GetFormattedYearMonth();
                var selectedDate = TestPicker.SelectedDate;
                
                ResultText.Text = $"格式化值: {value}\n选择日期: {selectedDate?.ToString("yyyy-MM") ?? "null"}";
            }
            catch (Exception ex)
            {
                ResultText.Text = $"错误: {ex.Message}";
            }
        }
    }
}
