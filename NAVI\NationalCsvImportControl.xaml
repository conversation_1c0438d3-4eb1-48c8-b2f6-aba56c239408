<UserControl x:Class="NAVI.NationalCsvImportControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ImportButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 导入文件列表样式 -->
        <Style x:Key="ImportFileListStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="国保連CSV取込" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF2196F3"
                   Margin="0,0,0,20"/>

        <!-- 操作按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Name="SelectCsvButton"
                    Content="CSVファイル取込"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FF2196F3"
                    Click="SelectCsvButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="选择要导入的CSV文件"/>
                </Button.ToolTip>
            </Button>

            <Button Name="DataMappingButton"
                    Content="データ検証"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FFFF9800"
                    IsEnabled="False"
                    Click="DataMappingButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="验证数据格式和字段映射"/>
                </Button.ToolTip>
            </Button>

            <Button Name="ImportExecuteButton"
                    Content="取込実行"
                    Style="{StaticResource ImportButtonStyle}"
                    Background="#FF4CAF50"
                    IsEnabled="False"
                    Click="ImportExecuteButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="执行数据导入"/>
                </Button.ToolTip>
            </Button>
        </StackPanel>

        <!-- 说明文字 -->
        <TextBlock Grid.Row="2" 
                   Text="国保連システムからダウンロードした公式データを管理します。"
                   FontSize="14"
                   Foreground="#FF666666"
                   Margin="0,0,0,15"/>

        <!-- 导入状态 -->
        <Border Grid.Row="3"
                Background="#FFF3E5F5"
                BorderBrush="#FFE1BEE7"
                BorderThickness="1"
                CornerRadius="4"
                Padding="15"
                Margin="0,0,0,15">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>

                <TextBlock Grid.Row="0" Text="取込状況:" FontWeight="Medium" FontSize="14" Margin="0,0,0,5"/>
                <TextBlock Grid.Row="1" Name="ImportStatusText"
                           Text="ファイル選択待ち..."
                           FontSize="13"
                           Foreground="#FF666666"
                           Margin="0,0,0,5"/>

                <!-- 导入结果详情 -->
                <StackPanel Grid.Row="2" Name="ImportResultPanel" Visibility="Collapsed" Margin="0,10,0,0">
                    <TextBlock Text="取込結果詳細:" FontWeight="Medium" FontSize="12" Margin="0,0,0,5"/>
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>

                        <StackPanel Grid.Column="0" Margin="0,0,20,0">
                            <TextBlock Text="選択ファイル:" FontSize="11" Foreground="#FF666666"/>
                            <TextBlock Name="SelectedFileText" Text="-" FontSize="11" FontWeight="Medium"/>
                        </StackPanel>

                        <StackPanel Grid.Column="1" Margin="0,0,20,0">
                            <TextBlock Text="処理レコード数:" FontSize="11" Foreground="#FF666666"/>
                            <TextBlock Name="ProcessedRecordsText" Text="-" FontSize="11" FontWeight="Medium" Foreground="#FF4CAF50"/>
                        </StackPanel>

                        <StackPanel Grid.Column="2" Margin="0,0,20,0">
                            <TextBlock Text="成功:" FontSize="11" Foreground="#FF666666"/>
                            <TextBlock Name="SuccessCountText" Text="-" FontSize="11" FontWeight="Medium" Foreground="#FF4CAF50"/>
                        </StackPanel>

                        <StackPanel Grid.Column="3">
                            <TextBlock Text="エラー:" FontSize="11" Foreground="#FF666666"/>
                            <TextBlock Name="ErrorCountText" Text="-" FontSize="11" FontWeight="Medium" Foreground="#FFF44336"/>
                        </StackPanel>
                    </Grid>
                </StackPanel>
            </Grid>
        </Border>

        <!-- 导入文件列表 -->
        <Border Grid.Row="4"
                Name="ImportFileListPanel"
                BorderBrush="#FFE0E0E0"
                BorderThickness="1"
                CornerRadius="4"
                Background="White"
                Visibility="Collapsed">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 列表标题 -->
                <Border Grid.Row="0" 
                        Background="#FFF5F5F5" 
                        BorderBrush="#FFE0E0E0" 
                        BorderThickness="0,0,0,1"
                        Padding="10">
                    <TextBlock Text="取込済みファイル一覧:" 
                               FontWeight="Medium" 
                               FontSize="14"/>
                </Border>

                <!-- 文件数据网格 -->
                <DataGrid Grid.Row="1"
                          Name="ImportedFilesGrid"
                          Style="{StaticResource ImportFileListStyle}"
                          ItemsSource="{Binding ImportedFiles}"
                          Margin="5">
                    <DataGrid.Columns>
                        <!-- 序号 -->
                        <DataGridTextColumn Header="No"
                                          Binding="{Binding ImportId}"
                                          Width="50">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Center"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 文件名 -->
                        <DataGridTextColumn Header="ファイル名"
                                          Binding="{Binding FileName}"
                                          Width="180"/>

                        <!-- 导入时间 -->
                        <DataGridTextColumn Header="取込時間"
                                          Binding="{Binding ImportTime}"
                                          Width="130"/>

                        <!-- 导入条数 -->
                        <DataGridTextColumn Header="取込件数"
                                          Binding="{Binding RecordCount}"
                                          Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="FontWeight" Value="Medium"/>
                                    <Setter Property="Foreground" Value="#FF4CAF50"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 成功条数 -->
                        <DataGridTextColumn Header="成功件数"
                                          Binding="{Binding SuccessCount}"
                                          Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FF4CAF50"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 错误条数 -->
                        <DataGridTextColumn Header="エラー件数"
                                          Binding="{Binding ErrorCount}"
                                          Width="80">
                            <DataGridTextColumn.ElementStyle>
                                <Style TargetType="TextBlock">
                                    <Setter Property="HorizontalAlignment" Value="Right"/>
                                    <Setter Property="Foreground" Value="#FFF44336"/>
                                </Style>
                            </DataGridTextColumn.ElementStyle>
                        </DataGridTextColumn>

                        <!-- 导入状态 -->
                        <DataGridTemplateColumn Header="状態" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,3" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding Status}" Value="取込成功">
                                                        <Setter Property="Background" Value="#FFE8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="取込失敗">
                                                        <Setter Property="Background" Value="#FFFFEAEA"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding Status}" Value="処理中">
                                                        <Setter Property="Background" Value="#FFF3E5F5"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding Status}" FontSize="10" FontWeight="Medium">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding Status}" Value="取込成功">
                                                            <Setter Property="Foreground" Value="#FF4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="取込失敗">
                                                            <Setter Property="Foreground" Value="#FFF44336"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding Status}" Value="処理中">
                                                            <Setter Property="Foreground" Value="#FF9C27B0"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 验证状态 -->
                        <DataGridTemplateColumn Header="検証状態" Width="100">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <Border CornerRadius="12" Padding="8,3" HorizontalAlignment="Center">
                                        <Border.Style>
                                            <Style TargetType="Border">
                                                <Style.Triggers>
                                                    <DataTrigger Binding="{Binding ValidationStatus}" Value="検証済み">
                                                        <Setter Property="Background" Value="#FFE8F5E8"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ValidationStatus}" Value="検証エラー">
                                                        <Setter Property="Background" Value="#FFFFEAEA"/>
                                                    </DataTrigger>
                                                    <DataTrigger Binding="{Binding ValidationStatus}" Value="未検証">
                                                        <Setter Property="Background" Value="#FFF5F5F5"/>
                                                    </DataTrigger>
                                                </Style.Triggers>
                                            </Style>
                                        </Border.Style>
                                        <TextBlock Text="{Binding ValidationStatus}" FontSize="10" FontWeight="Medium">
                                            <TextBlock.Style>
                                                <Style TargetType="TextBlock">
                                                    <Style.Triggers>
                                                        <DataTrigger Binding="{Binding ValidationStatus}" Value="検証済み">
                                                            <Setter Property="Foreground" Value="#FF4CAF50"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding ValidationStatus}" Value="検証エラー">
                                                            <Setter Property="Foreground" Value="#FFF44336"/>
                                                        </DataTrigger>
                                                        <DataTrigger Binding="{Binding ValidationStatus}" Value="未検証">
                                                            <Setter Property="Foreground" Value="#FF666666"/>
                                                        </DataTrigger>
                                                    </Style.Triggers>
                                                </Style>
                                            </TextBlock.Style>
                                        </TextBlock>
                                    </Border>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>

                        <!-- 操作按钮 -->
                        <DataGridTemplateColumn Header="操作" Width="140">
                            <DataGridTemplateColumn.CellTemplate>
                                <DataTemplate>
                                    <StackPanel Orientation="Horizontal" HorizontalAlignment="Center">
                                        <Button Content="詳細"
                                                Background="#FF2196F3"
                                                Foreground="White"
                                                FontSize="10"
                                                Height="22"
                                                Width="35"
                                                Margin="2"
                                                Click="ViewDetailsButton_Click"
                                                Tag="{Binding}"/>
                                        <Button Content="再検証"
                                                Background="#FFFF9800"
                                                Foreground="White"
                                                FontSize="10"
                                                Height="22"
                                                Width="45"
                                                Margin="2"
                                                Click="RevalidateButton_Click"
                                                Tag="{Binding}"/>
                                        <Button Content="削除"
                                                Background="#FFF44336"
                                                Foreground="White"
                                                FontSize="10"
                                                Height="22"
                                                Width="35"
                                                Margin="2"
                                                Click="DeleteImportButton_Click"
                                                Tag="{Binding}"/>
                                    </StackPanel>
                                </DataTemplate>
                            </DataGridTemplateColumn.CellTemplate>
                        </DataGridTemplateColumn>
                    </DataGrid.Columns>
                </DataGrid>


            </Grid>
        </Border>

        <!-- 进度和统计信息 -->
        <Grid Grid.Row="5"
              Name="ProgressAndStatsPanel"
              Margin="0,15,0,0"
              Visibility="Collapsed">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>

            <!-- 进度条 -->
            <ProgressBar Grid.Row="0" 
                         Name="ImportProgressBar"
                         Height="8"
                         Margin="0,0,0,10"
                         Visibility="Collapsed"/>

            <!-- 统计信息 -->
            <StackPanel Grid.Row="1" Orientation="Horizontal">
                <TextBlock Text="総ファイル数: " FontWeight="Medium"/>
                <TextBlock Name="TotalFilesText" Text="2" Foreground="#FF2196F3"/>
                <TextBlock Text=" | 総レコード数: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="TotalRecordsText" Text="2,430" Foreground="#FF4CAF50"/>
                <TextBlock Text=" | 最終更新: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="LastUpdateText" Text="2025-05-15 14:30" Foreground="#FF666666"/>
            </StackPanel>
        </Grid>
    </Grid>
</UserControl>
