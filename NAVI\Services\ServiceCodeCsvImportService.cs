using System;
using System.Collections.Generic;
using System.Data;
using System.IO;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using NAVI.Services.DAL;

namespace NAVI.Services
{
    /// <summary>
    /// ServiceCode CSV导入服务
    /// </summary>
    public class ServiceCodeCsvImportService : IDisposable
    {
        private readonly DatabaseManager _databaseManager;
        private readonly ServiceCodeRepository _serviceCodeRepository;

        public ServiceCodeCsvImportService()
        {
            _databaseManager = new DatabaseManager();
            _serviceCodeRepository = new ServiceCodeRepository(_databaseManager._databaseService);
        }

        /// <summary>
        /// 导入ServiceCode CSV数据
        /// </summary>
        /// <param name="csvFilePath">CSV文件路径</param>
        /// <param name="encoding">文件编码，默认为UTF-8</param>
        /// <returns>导入结果</returns>
        public async Task<CsvImportResult> ImportServiceCodeCsvAsync(string csvFilePath, Encoding encoding = null)
        {
            var result = new CsvImportResult
            {
                FileName = Path.GetFileName(csvFilePath),
                ImportTime = DateTime.Now,
                IsSuccess = false
            };

            try
            {
                if (encoding == null)
                    encoding = Encoding.UTF8;

                // 读取CSV文件
                var csvData = ReadCsvFile(csvFilePath, encoding);
                if (csvData.Rows.Count == 0)
                {
                    result.ErrorMessage = "CSVファイルが空か、形式が正しくありません";
                    return result;
                }

                // 映射CSV列到ServiceCodeMaster字段
                var mappedServiceCodes = MapCsvToServiceCodes(csvData);
                if (mappedServiceCodes.Count == 0)
                {
                    result.ErrorMessage = "有効なサービスコードデータが見つかりません";
                    return result;
                }

                // 验证数据
                var validationResult = await ValidateServiceCodes(mappedServiceCodes);
                if (!validationResult.IsValid)
                {
                    result.ErrorMessage = validationResult.ErrorMessage;
                    result.ValidationErrors = validationResult.Warnings;
                    return result;
                }

                // 导入到数据库
                var importedCount = await ImportServiceCodesToDatabase(mappedServiceCodes);

                result.IsSuccess = true;
                result.TotalRecords = csvData.Rows.Count;
                result.ImportedRecords = importedCount;
                result.SuccessMessage = $"成功取込 {importedCount} 件のサービスコードレコード";

                return result;
            }
            catch (Exception ex)
            {
                result.ErrorMessage = $"取込失敗：{ex.Message}";
                return result;
            }
        }

        /// <summary>
        /// 读取CSV文件
        /// </summary>
        private DataTable ReadCsvFile(string filePath, Encoding encoding)
        {
            var dataTable = new DataTable();
            var lines = File.ReadAllLines(filePath, encoding);

            if (lines.Length == 0)
                return dataTable;

            // 解析表头
            var headers = ParseCsvLine(lines[0]);
            foreach (var header in headers)
            {
                dataTable.Columns.Add(header.Trim());
            }

            // 解析数据行
            for (int i = 1; i < lines.Length; i++)
            {
                var values = ParseCsvLine(lines[i]);
                if (values.Length > 0 && !string.IsNullOrWhiteSpace(string.Join("", values)))
                {
                    var row = dataTable.NewRow();
                    for (int j = 0; j < Math.Min(values.Length, dataTable.Columns.Count); j++)
                    {
                        row[j] = values[j]?.Trim() ?? "";
                    }
                    dataTable.Rows.Add(row);
                }
            }

            return dataTable;
        }

        /// <summary>
        /// 解析CSV行
        /// </summary>
        private string[] ParseCsvLine(string line)
        {
            var result = new List<string>();
            var current = new StringBuilder();
            bool inQuotes = false;

            for (int i = 0; i < line.Length; i++)
            {
                char c = line[i];

                if (c == '"')
                {
                    inQuotes = !inQuotes;
                }
                else if (c == ',' && !inQuotes)
                {
                    result.Add(current.ToString());
                    current.Clear();
                }
                else
                {
                    current.Append(c);
                }
            }

            result.Add(current.ToString());
            return result.ToArray();
        }

        /// <summary>
        /// 映射CSV数据到ServiceCodeMaster对象
        /// </summary>
        private List<ServiceCodeMaster> MapCsvToServiceCodes(DataTable csvData)
        {
            var serviceCodes = new List<ServiceCodeMaster>();
            var columnMapping = CreateColumnMapping(csvData.Columns);

            foreach (DataRow row in csvData.Rows)
            {
                var serviceCode = new ServiceCodeMaster();
                bool hasValidData = false;

                // 映射各个字段
                if (columnMapping.ContainsKey("サービスコード"))
                {
                    serviceCode.サービスコード = row[columnMapping["サービスコード"]]?.ToString()?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(serviceCode.サービスコード)) hasValidData = true;
                }

                if (columnMapping.ContainsKey("サービス内容略称"))
                    serviceCode.サービス内容略称 = row[columnMapping["サービス内容略称"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("障害支援区分"))
                    serviceCode.障害支援区分 = row[columnMapping["障害支援区分"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("合成単位"))
                    serviceCode.合成単位 = row[columnMapping["合成単位"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("級地コード"))
                {
                    serviceCode.級地コード = row[columnMapping["級地コード"]]?.ToString()?.Trim() ?? "";
                    if (!string.IsNullOrEmpty(serviceCode.級地コード)) hasValidData = true;
                }

                if (columnMapping.ContainsKey("単位数単価"))
                    serviceCode.単位数単価 = row[columnMapping["単位数単価"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("国費単価"))
                    serviceCode.国費単価 = row[columnMapping["国費単価"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("旧身体療護"))
                    serviceCode.旧身体療護 = row[columnMapping["旧身体療護"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("都単価"))
                    serviceCode.都単価 = row[columnMapping["都単価"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("キーコード"))
                    serviceCode.キーコード = row[columnMapping["キーコード"]]?.ToString()?.Trim() ?? "";

                if (columnMapping.ContainsKey("都加算単価"))
                    serviceCode.都加算単価 = row[columnMapping["都加算単価"]]?.ToString()?.Trim() ?? "";

                // 只有包含必要字段的记录才添加（サービスコード和級地コード不为空）
                if (hasValidData && !string.IsNullOrEmpty(serviceCode.サービスコード) && !string.IsNullOrEmpty(serviceCode.級地コード))
                {
                    serviceCodes.Add(serviceCode);
                }
            }

            return serviceCodes;
        }

        /// <summary>
        /// 创建列映射
        /// </summary>
        private Dictionary<string, int> CreateColumnMapping(DataColumnCollection columns)
        {
            var mapping = new Dictionary<string, int>();
            var fieldMappings = new Dictionary<string, string[]>
            {
                { "サービスコード", new[] { "サービスコード", "服务代码", "ServiceCode", "Code" } },
                { "サービス内容略称", new[] { "サービス内容略称", "服务内容略称", "服务内容", "ServiceContent", "Content" } },
                { "障害支援区分", new[] { "障害支援区分", "障害支援\r\n区分", "障害支援\n区分", "障害区分", "DisabilityCategory" } },
                { "合成単位", new[] { "合成単位", "合成\r\n単位", "合成\n単位", "单位", "Unit" } },
                { "級地コード", new[] { "級地コード", "級地\r\nコード", "級地\nコード", "级地代码", "RegionCode" } },
                { "単位数単価", new[] { "単位数単価", "单位数单价", "UnitPrice" } },
                { "国費単価", new[] { "国費単価", "国費\r\n単価", "国費\n単価", "国费单价", "NationalPrice" } },
                { "旧身体療護", new[] { "旧身体療護", "旧身体\r\n療護", "旧身体\n療護", "旧身体疗护", "OldPhysicalCare" } },
                { "都単価", new[] { "都単価", "都单价", "CityPrice" } },
                { "キーコード", new[] { "キーコード", "键代码", "KeyCode" } },
                { "都加算単価", new[] { "都加算単価", "都加算\r\n単価", "都加算\n単価", "都加算单价", "CityAdditionalPrice" } }
            };

            for (int i = 0; i < columns.Count; i++)
            {
                var columnName = columns[i].ColumnName.Trim();
                
                foreach (var fieldMapping in fieldMappings)
                {
                    if (fieldMapping.Value.Any(alias => string.Equals(alias, columnName, StringComparison.OrdinalIgnoreCase)))
                    {
                        mapping[fieldMapping.Key] = i;
                        break;
                    }
                }
            }

            return mapping;
        }

        /// <summary>
        /// 验证ServiceCode数据
        /// </summary>
        private async Task<ValidationResult> ValidateServiceCodes(List<ServiceCodeMaster> serviceCodes)
        {
            var result = new ValidationResult { IsValid = true };
            var errors = new List<string>();

            // 检查重复的サービスコード
           /* var duplicateServiceCodes = serviceCodes.GroupBy(s => s.サービスコード)
                .Where(g => g.Count() > 1)
                .Select(g => g.Key);

            if (duplicateServiceCodes.Any())
            {
                errors.Add($"CSV中に重複するサービスコードがあります: {string.Join(", ", duplicateServiceCodes)}");
            }*/

            // 检查必填字段
            for (int i = 0; i < serviceCodes.Count; i++)
            {
                var serviceCode = serviceCodes[i];
                var rowNumber = i + 2; // CSV行号（包含标题行）

                if (string.IsNullOrEmpty(serviceCode.サービスコード))
                {
                    errors.Add($"行 {rowNumber}: サービスコードが空です");
                }

                if (string.IsNullOrEmpty(serviceCode.級地コード))
                {
                    errors.Add($"行 {rowNumber}: 級地コードが空です");
                }
            }

            // 检查数据库中是否已存在相同的サービスコード
            foreach (var serviceCode in serviceCodes)
            {
                if (!string.IsNullOrEmpty(serviceCode.サービスコード))
                {
                    var existing = await _serviceCodeRepository.GetByServiceCodeAsync(serviceCode.サービスコード);
                    if (existing != null)
                    {
                        errors.Add($"サービスコード {serviceCode.サービスコード} は既にデータベースに存在します");
                    }
                }
            }

            if (errors.Any())
            {
                result.IsValid = false;
                result.ErrorMessage = "データ検証に失敗しました";
                result.Warnings = errors;
            }

            return result;
        }

        /// <summary>
        /// 导入ServiceCode到数据库
        /// </summary>
        private async Task<int> ImportServiceCodesToDatabase(List<ServiceCodeMaster> serviceCodes)
        {
            int importedCount = 0;

            foreach (var serviceCode in serviceCodes)
            {
                try
                {
                    await _serviceCodeRepository.InsertAsync(serviceCode);
                    importedCount++;
                }
                catch (Exception ex)
                {
                    // 记录错误但继续处理其他记录
                    System.Diagnostics.Debug.WriteLine($"サービスコード導入失敗 {serviceCode.サービスコード}: {ex.Message}");
                }
            }

            return importedCount;
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
            //_serviceCodeRepository?.Dispose();
        }
    }
}
