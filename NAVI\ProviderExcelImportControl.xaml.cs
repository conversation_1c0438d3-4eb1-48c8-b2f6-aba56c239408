using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.IO;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using Microsoft.Win32;
using NAVI.Models;
using NAVI.Services;

namespace NAVI
{
    /// <summary>
    /// 事业者Excel导入控件
    /// </summary>
    public partial class ProviderExcelImportControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<ExcelFileInfo> _selectedFiles;
        private bool _isImporting = false;
        private const int MAX_FILES = 200;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 选中的文件列表
        /// </summary>
        public ObservableCollection<ExcelFileInfo> SelectedFiles
        {
            get => _selectedFiles;
            set
            {
                _selectedFiles = value;
                OnPropertyChanged(nameof(SelectedFiles));
            }
        }

        public ProviderExcelImportControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            SelectedFiles = new ObservableCollection<ExcelFileInfo>();
            DataContext = this;
            UpdateUI();
        }

        /// <summary>
        /// 选择文件按钮点击事件
        /// </summary>
        private void SelectFilesButton_Click(object sender, RoutedEventArgs e)
        {
            var openFileDialog = new OpenFileDialog
            {
                Title = "选择Excel文件",
                Filter = "Excel文件 (*.xlsx;*.xls)|*.xlsx;*.xls|所有文件 (*.*)|*.*",
                Multiselect = true,
                CheckFileExists = true
            };

            if (openFileDialog.ShowDialog() == true)
            {
                AddFiles(openFileDialog.FileNames);
            }
        }

        /// <summary>
        /// 添加文件到列表
        /// </summary>
        private void AddFiles(string[] filePaths)
        {
            try
            {
                var newFiles = new List<ExcelFileInfo>();

                foreach (var filePath in filePaths)
                {
                    // 检查文件是否已存在
                    if (SelectedFiles.Any(f => f.FilePath.Equals(filePath, StringComparison.OrdinalIgnoreCase)))
                    {
                        continue;
                    }

                    // 检查文件数量限制
                    if (SelectedFiles.Count >= MAX_FILES)
                    {
                        MessageBox.Show($"最大只能选择{MAX_FILES}个文件", "提示", 
                            MessageBoxButton.OK, MessageBoxImage.Warning);
                        break;
                    }

                    var fileInfo = new FileInfo(filePath);
                    var excelFileInfo = new ExcelFileInfo
                    {
                        FileName = fileInfo.Name,
                        FilePath = filePath,
                        FileSize = FormatFileSize(fileInfo.Length),
                        LastModified = DateTime.Now.ToString("yyyy/MM/dd HH:mm"),
                        Status = "待処理",
                        RecordCount = "-"
                    };
                    //fileInfo.LastWriteTime.ToString("yyyy/MM/dd HH:mm"),
                    newFiles.Add(excelFileInfo);
                }

                // 添加到集合
                foreach (var file in newFiles)
                {
                    SelectedFiles.Add(file);
                }

                UpdateUI();

                if (newFiles.Count > 0)
                {
                    StatusText.Text = $"{newFiles.Count}個のファイルを追加しました";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"文件添加失败：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 导入按钮点击事件
        /// </summary>
        private async void ImportButton_Click(object sender, RoutedEventArgs e)
        {
            if (SelectedFiles.Count == 0)
            {
                MessageBox.Show("请先选择要导入的文件", "提示", 
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"确定要导入{SelectedFiles.Count}个Excel文件吗？", "确认导入",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                await StartImportAsync();
            }
        }

        /// <summary>
        /// 开始导入处理
        /// </summary>
        private async Task StartImportAsync()
        {
            try
            {
                _isImporting = true;
                UpdateUI();

                ImportProgressBar.Visibility = Visibility.Visible;
                ImportProgressBar.Maximum = SelectedFiles.Count;
                ImportProgressBar.Value = 0;

                StatusText.Text = "導入処理中...";

                int processedCount = 0;
                int successCount = 0;

                foreach (var fileInfo in SelectedFiles)
                {
                    try
                    {
                        fileInfo.Status = "処理中";
                        
                        // 模拟导入处理
                        await Task.Delay(500); // 模拟处理时间
                        
                        // 这里应该调用实际的Excel导入逻辑
                        var recordCount = await ImportExcelFileAsync(fileInfo.FilePath);
                        
                        fileInfo.Status = "完了";
                        fileInfo.RecordCount = recordCount.ToString();
                        successCount++;
                    }
                    catch (Exception ex)
                    {
                        fileInfo.Status = "エラー";
                        fileInfo.RecordCount = "0";
                        // 记录错误但继续处理其他文件
                        System.Diagnostics.Debug.WriteLine($"导入文件失败 {fileInfo.FileName}: {ex.Message}");
                    }

                    processedCount++;
                    ImportProgressBar.Value = processedCount;
                    ProcessedCountText.Text = processedCount.ToString();

                    // 刷新UI
                    await Task.Delay(10);
                }

                StatusText.Text = $"導入完了 - 成功: {successCount}, 失敗: {processedCount - successCount}";
                
                MessageBox.Show($"导入完成！\n成功：{successCount}个文件\n失败：{processedCount - successCount}个文件", 
                    "导入完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"导入过程中发生错误：{ex.Message}", "错误", 
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "導入エラー";
            }
            finally
            {
                _isImporting = false;
                ImportProgressBar.Visibility = Visibility.Collapsed;
                UpdateUI();
            }
        }

        /// <summary>
        /// 导入单个Excel文件
        /// </summary>
        private async Task<int> ImportExcelFileAsync(string filePath)
        {
            try
            {
                var importService = new ProviderExcelImportService();
                var result = await importService.ImportProviderExcelAsync(filePath);
                return result.ImportedRecordCount;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"导入Excel文件失败: {ex.Message}");
                throw;
            }
        }

      
        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            if (_isImporting)
            {
                var result = MessageBox.Show("确定要取消导入操作吗？", "确认取消",
                    MessageBoxButton.YesNo, MessageBoxImage.Question);
                
                if (result == MessageBoxResult.Yes)
                {
                    // 这里应该实现取消导入的逻辑
                    _isImporting = false;
                    StatusText.Text = "導入キャンセル";
                    ImportProgressBar.Visibility = Visibility.Collapsed;
                    UpdateUI();
                }
            }
            else
            {
                // 清空文件列表
                SelectedFiles.Clear();
                UpdateUI();
                StatusText.Text = "ファイル選択待ち...";
            }
        }

        /// <summary>
        /// 删除文件按钮点击事件
        /// </summary>
        private void RemoveFileButton_Click(object sender, RoutedEventArgs e)
        {
            if (sender is Button button && button.Tag is ExcelFileInfo fileInfo)
            {
                SelectedFiles.Remove(fileInfo);
                UpdateUI();
                StatusText.Text = $"ファイル「{fileInfo.FileName}」を削除しました";
            }
        }

        /// <summary>
        /// 创建测试文件按钮点击事件
        /// </summary>
        private void CreateTestFileButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "保存测试Excel文件",
                    Filter = "Excel文件 (*.xlsx)|*.xlsx",
                    FileName = "test_provider_excel.xlsx"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    Services.ProviderExcelTestCreator.CreateTestProviderExcel(saveFileDialog.FileName);
                    MessageBox.Show($"测试文件已创建：{saveFileDialog.FileName}", "成功",
                        MessageBoxButton.OK, MessageBoxImage.Information);

                    StatusText.Text = "テストファイルを作成しました";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"创建测试文件失败：{ex.Message}", "错误",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 更新UI状态
        /// </summary>
        private void UpdateUI()
        {
            var hasFiles = SelectedFiles.Count > 0;
            
            ImportButton.IsEnabled = hasFiles && !_isImporting;
            SelectFilesButton.IsEnabled = !_isImporting;
            
            EmptyStateText.Visibility = hasFiles ? Visibility.Collapsed : Visibility.Visible;
            
            FileCountText.Text = SelectedFiles.Count.ToString();
        }

        /// <summary>
        /// 格式化文件大小
        /// </summary>
        private string FormatFileSize(long bytes)
        {
            string[] sizes = { "B", "KB", "MB", "GB" };
            double len = bytes;
            int order = 0;
            while (len >= 1024 && order < sizes.Length - 1)
            {
                order++;
                len = len / 1024;
            }
            return $"{len:0.##} {sizes[order]}";
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }

    /// <summary>
    /// Excel文件信息类
    /// </summary>
    public class ExcelFileInfo : INotifyPropertyChanged
    {
        private string _status;
        private string _recordCount;

        public string FileName { get; set; }
        public string FilePath { get; set; }
        public string FileSize { get; set; }
        public string LastModified { get; set; }
        
        public string Status
        {
            get => _status;
            set
            {
                _status = value;
                OnPropertyChanged(nameof(Status));
            }
        }
        
        public string RecordCount
        {
            get => _recordCount;
            set
            {
                _recordCount = value;
                OnPropertyChanged(nameof(RecordCount));
            }
        }

        public event PropertyChangedEventHandler PropertyChanged;
        
        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
