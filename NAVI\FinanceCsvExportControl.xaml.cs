﻿﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using Microsoft.Win32;
using System.IO;

namespace NAVI
{
    /// <summary>
    /// FinanceCsvExportControl.xaml 的交互逻辑
    /// </summary>
    public partial class FinanceCsvExportControl : UserControl
    {
        public FinanceCsvExportControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            // 隐藏成功消息面板
            SuccessMessagePanel.Visibility = Visibility.Collapsed;
        }

        /// <summary>
        /// 出力实行按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                // 获取选择的月份
                var selectedMonth = (TargetMonthComboBox.SelectedItem as ComboBoxItem)?.Content?.ToString();
                if (string.IsNullOrEmpty(selectedMonth))
                {
                    MessageBox.Show("対象年月を選択してください。", "入力エラー", 
                        MessageBoxButton.OK, MessageBoxImage.Warning);
                    return;
                }

                // 显示文件保存对话框
                var saveFileDialog = new SaveFileDialog
                {
                    Title = "財務CSVファイルを保存",
                    Filter = "CSVファイル (*.csv)|*.csv|すべてのファイル (*.*)|*.*",
                    FilterIndex = 1,
                    RestoreDirectory = true,
                    FileName = $"財務データ_{selectedMonth}_{DateTime.Now:yyyyMMdd_HHmmss}.csv"
                };

                if (saveFileDialog.ShowDialog() == true)
                {
                    // 执行CSV导出
                    ExportFinanceCsv(saveFileDialog.FileName, selectedMonth);
                    
                    // 显示成功消息
                    ShowSuccessMessage($"財務CSVファイルが正常に出力されました。\n保存先: {saveFileDialog.FileName}");
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"財務CSV出力でエラーが発生しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 取消按钮点击事件
        /// </summary>
        private void CancelButton_Click(object sender, RoutedEventArgs e)
        {
            // 隐藏成功消息面板
            SuccessMessagePanel.Visibility = Visibility.Collapsed;
            
            // 重置选择
            TargetMonthComboBox.SelectedIndex = 0;
        }

        /// <summary>
        /// 执行财务CSV导出
        /// </summary>
        /// <param name="filePath">文件路径</param>
        /// <param name="targetMonth">目标月份</param>
        private void ExportFinanceCsv(string filePath, string targetMonth)
        {
            try
            {
                // 创建CSV内容
                var csvContent = new StringBuilder();
                
                // 添加CSV头部
                csvContent.AppendLine("No,登録日,事業者番号,事業者名称,サービス提供年月,請求金額,受給者番号,サービスコード,サービス内容,算定単価額,利用日数,当月算定額");
                
                // TODO: 从数据库获取实际的财务数据
                // 这里添加示例数据，实际应用中应该从数据库查询
                for (int i = 1; i <= 10; i++)
                {
                    csvContent.AppendLine($"{i},2025/01/15,1234567890,サンプル事業者{i},{targetMonth},150000,REC{i:D6},241111,福祉短期入所Ⅰ６,8500,15,127500");
                }
                
                // 写入文件
                File.WriteAllText(filePath, csvContent.ToString(), Encoding.UTF8);
            }
            catch (Exception ex)
            {
                throw new Exception($"CSV出力処理に失敗しました: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示成功消息
        /// </summary>
        /// <param name="message">消息内容</param>
        private void ShowSuccessMessage(string message)
        {
            ExportResultText.Text = message;
            SuccessMessagePanel.Visibility = Visibility.Visible;
        }
    }
}
