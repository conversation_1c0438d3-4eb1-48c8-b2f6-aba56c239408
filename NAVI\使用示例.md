# 事业者Excel导入功能使用示例

## 功能演示步骤

### 1. 创建测试文件
```
1. 打开应用程序
2. 导航到"事业者Excel导入"功能
3. 点击"テストファイル作成"按钮
4. 选择保存位置，创建测试Excel文件
5. 生成的文件包含标准格式的示例数据
```

### 2. 查看测试文件内容
生成的测试文件包含以下示例数据：

**基本信息：**
- 年月：令和7年2月
- 受給者証番号：1234567890
- 支給決定障害者氏名：鈴木太郎
- 事業所番号：1234567890
- 事業者名称：サポートハウス東京
- 障害支援区分：6
- 地域区分：1級地

**服务明细：**
- 福祉短期入所Ⅰ６ (241111)
- 福祉短期入所Ⅰ５・大規模減算 (241115)
- 短期医療連携体制加算系列 (246063)
- 精神科医療連携体制加算 (249919)

### 3. 执行导入
```
1. 点击"選択ファイル"按钮
2. 选择刚才创建的测试Excel文件
3. 文件会显示在文件列表中
4. 点击"一括ファイル取込実行"开始导入
5. 观察进度条和状态信息
```

### 4. 查看导入结果
导入完成后，系统会显示：
- 导入的记录数量
- 匹配成功的事业者数量
- 匹配成功的服务代码数量
- 任何警告或错误信息

## 数据处理示例

### 原始Excel数据
```
年月: 令和7年2月
受給者証番号: 1234567890
支給決定障害者氏名: 鈴木太郎
事業所番号: 1234567890
事業者名称: サポートハウス東京

服务明细:
- 241111 | 福祉短期入所Ⅰ６ | 10210 | 28 | 285,880
- 241115 | 福祉短期入所Ⅰ５ | 10210 | 12 | 122,520
- 246063 | 短期医療連携体制加算Ⅰ１ | 698 | - | -
```

### 汇总整理后的数据
系统会将数据转换为标准格式：

**记录1：**
```
No: 1
登録日: 2025/01/10
事業者番号: 1234567890
事業者名称: サポートハウス東京
サービス提供年月: 令和7年2月
受給者番号: 1234567890
支給決定障害者氏名: 鈴木太郎
障害支援区分: 6
地域区分: 1級地
開始年月日: 2025/02/01
終了年月日: 2025/02/28
利用日数全体: 28
サービスコード: 241111
サービス内容: 福祉短期入所Ⅰ６
算定単価額: 10210
利用日数: 28
当月算定額: 285880
status: 利用中
```

**记录2：**
```
No: 2
登録日: 2025/01/10
事業者番号: 1234567890
事業者名称: サポートハウス東京
サービス提供年月: 令和7年2月
受給者番号: 1234567890
支給決定障害者氏名: 鈴木太郎
障害支援区分: 6
地域区分: 1級地
開始年月日: 2025/02/01
終了年月日: 2025/02/28
利用日数全体: 28
サービスコード: 241115
サービス内容: 福祉短期入所Ⅰ５・大規模減算
算定単価額: 10210
利用日数: 12
当月算定額: 122520
status: 利用中
```

## 数据匹配示例

### 事业者数据匹配
```
Excel中的事業所番号: 1234567890
↓ 匹配
事业者数据表中的记录:
- 事業者番号: 1234567890
- 事業者郵便番号: 123-4567
- 事業者住所: 東京都新宿区...
- 代表者名: 田中太郎
- 代表者役職: 代表取締役
```

### 服务代码数据匹配
```
Excel中的サービスコード: 241111
↓ 匹配
服务代码数据表中的记录:
- サービスコード: 241111
- サービス名称: 福祉短期入所Ⅰ６
- 単価区分: 地域区分1級地
- 障害支援区分: 障害支援区分6
```

## 错误处理示例

### 数据验证错误
```
错误信息: "受給者証番号不能为空"
处理方式: 停止导入，显示错误信息
```

### 匹配失败警告
```
警告信息: "服务代码 999999 未找到匹配记录"
处理方式: 使用原始数据，记录警告信息
```

### 格式错误警告
```
警告信息: "开始年月日格式可能不正确: 2025-2-1"
处理方式: 尝试格式转换，记录警告信息
```

## 批量导入示例

### 多文件处理
```
选择文件:
- provider_data_202501.xlsx (5个服务明细)
- provider_data_202502.xlsx (8个服务明细)
- provider_data_202503.xlsx (3个服务明细)

导入结果:
- 总文件数: 3
- 成功文件数: 3
- 失败文件数: 0
- 总记录数: 16
- 成功记录数: 16
```

## 数据验证检查点

1. **必要字段检查**
   - 受給者証番号
   - 支給決定障害者氏名
   - 事業所番号
   - 服务明细

2. **格式验证**
   - 日期格式
   - 数值格式
   - 文本长度

3. **业务逻辑验证**
   - 事业者存在性
   - 服务代码有效性
   - 日期合理性

这个功能确保了数据的准确性和一致性，大大简化了从Excel文件到数据库的数据迁移过程。
