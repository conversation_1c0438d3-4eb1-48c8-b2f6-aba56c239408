<UserControl x:Class="NAVI.DataReconciliationControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ReconciliationButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 月份选择样式 -->
        <Style x:Key="MonthComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="データ照合" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF2196F3"
                   Margin="0,0,0,20"/>

        <!-- 操作区域 -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 月份选择 -->
            <ComboBox Grid.Column="0"
                      Name="MonthComboBox"
                      Style="{StaticResource MonthComboBoxStyle}"
                      SelectedIndex="0">
                <ComboBoxItem Content="令和7年9月度"/>
                <ComboBoxItem Content="令和7年8月度"/>
                <ComboBoxItem Content="令和7年7月度"/>
                <ComboBoxItem Content="令和7年6月度"/>
                <ComboBoxItem Content="令和7年5月度"/>
                <ComboBoxItem Content="令和7年4月度"/>
                <ComboBoxItem Content="令和7年3月度"/>
                <ComboBoxItem Content="令和7年2月度"/>
                <ComboBoxItem Content="令和7年1月度"/>
            </ComboBox>

            <!-- 操作按钮 -->
            <StackPanel Grid.Column="1" Orientation="Horizontal">
                <Button Name="StartReconciliationButton"
                        Content="照合実行"
                        Style="{StaticResource ReconciliationButtonStyle}"
                        Background="#FF4CAF50"
                        Click="StartReconciliationButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="开始执行数据照合"/>
                    </Button.ToolTip>
                </Button>

                <Button Name="CancelButton"
                        Content="キャンセル"
                        Style="{StaticResource ReconciliationButtonStyle}"
                        Background="#FFF44336"
                        Click="CancelButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="取消当前操作"/>
                    </Button.ToolTip>
                </Button>
            </StackPanel>
        </Grid>

        <!-- 说明文字 -->
        <TextBlock Grid.Row="2" 
                   Text="事業者データと国保連データを照合します。"
                   FontSize="14"
                   Foreground="#FF666666"
                   Margin="0,0,0,15"/>

        <!-- 照合结果显示区域 -->
        <Border Grid.Row="3" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="1" 
                CornerRadius="4"
                Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 结果标题栏 -->
                <Border Grid.Row="0" 
                        Background="#FFF5F5F5" 
                        BorderBrush="#FFE0E0E0" 
                        BorderThickness="0,0,0,1"
                        Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0"
                                   Text="照合結果" 
                                   FontWeight="Medium" 
                                   FontSize="16"/>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="処理状況: " FontWeight="Medium"/>
                            <TextBlock Name="ProcessStatusText" 
                                       Text="待機中" 
                                       Foreground="#FF666666"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 结果内容区域 -->
                <ScrollViewer Grid.Row="1" 
                             VerticalScrollBarVisibility="Auto"
                             HorizontalScrollBarVisibility="Auto"
                             Padding="15">
                    <Grid>
                        <!-- 照合统计信息 -->
                        <Grid Name="ReconciliationStats" Visibility="Collapsed">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                                <RowDefinition Height="Auto"/>
                            </Grid.RowDefinitions>

                            <!-- 统计卡片 -->
                            <Grid Grid.Row="0" Margin="0,0,0,20">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="*"/>
                                </Grid.ColumnDefinitions>

                                <!-- 总记录数 -->
                                <Border Grid.Column="0" 
                                        Background="#FFE3F2FD" 
                                        BorderBrush="#FF2196F3" 
                                        BorderThickness="1" 
                                        CornerRadius="8"
                                        Padding="15"
                                        Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="総レコード数" 
                                                   FontSize="12" 
                                                   Foreground="#FF666666" 
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Name="TotalRecordsText" 
                                                   Text="2,430" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#FF2196F3" 
                                                   HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- 匹配记录数 -->
                                <Border Grid.Column="1" 
                                        Background="#FFE8F5E8" 
                                        BorderBrush="#FF4CAF50" 
                                        BorderThickness="1" 
                                        CornerRadius="8"
                                        Padding="15"
                                        Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="一致レコード数" 
                                                   FontSize="12" 
                                                   Foreground="#FF666666" 
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Name="MatchedRecordsText" 
                                                   Text="2,180" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#FF4CAF50" 
                                                   HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- 不匹配记录数 -->
                                <Border Grid.Column="2" 
                                        Background="#FFFCE4EC" 
                                        BorderBrush="#FFF44336" 
                                        BorderThickness="1" 
                                        CornerRadius="8"
                                        Padding="15"
                                        Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="不一致レコード数" 
                                                   FontSize="12" 
                                                   Foreground="#FF666666" 
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Name="UnmatchedRecordsText" 
                                                   Text="250" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#FFF44336" 
                                                   HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>

                                <!-- 匹配率 -->
                                <Border Grid.Column="3" 
                                        Background="#FFFFF3E0" 
                                        BorderBrush="#FFFF9800" 
                                        BorderThickness="1" 
                                        CornerRadius="8"
                                        Padding="15"
                                        Margin="5">
                                    <StackPanel HorizontalAlignment="Center">
                                        <TextBlock Text="一致率" 
                                                   FontSize="12" 
                                                   Foreground="#FF666666" 
                                                   HorizontalAlignment="Center"/>
                                        <TextBlock Name="MatchRateText" 
                                                   Text="89.7%" 
                                                   FontSize="24" 
                                                   FontWeight="Bold" 
                                                   Foreground="#FFFF9800" 
                                                   HorizontalAlignment="Center"/>
                                    </StackPanel>
                                </Border>
                            </Grid>

                            <!-- 详细结果表格 -->
                            <TextBlock Grid.Row="1" 
                                       Text="詳細結果:" 
                                       FontSize="16" 
                                       FontWeight="Medium" 
                                       Margin="0,20,0,10"/>

                            <DataGrid Grid.Row="2" 
                                      Name="ReconciliationResultGrid"
                                      AutoGenerateColumns="False"
                                      CanUserAddRows="False"
                                      CanUserDeleteRows="False"
                                      IsReadOnly="True"
                                      GridLinesVisibility="Horizontal"
                                      HeadersVisibility="Column"
                                      Height="200">
                                <DataGrid.Columns>
                                    <DataGridTextColumn Header="項目" Binding="{Binding Category}" Width="150"/>
                                    <DataGridTextColumn Header="事業者データ数" Binding="{Binding ProviderCount}" Width="120"/>
                                    <DataGridTextColumn Header="国保連データ数" Binding="{Binding NationalCount}" Width="120"/>
                                    <DataGridTextColumn Header="一致数" Binding="{Binding MatchedCount}" Width="100"/>
                                    <DataGridTextColumn Header="不一致数" Binding="{Binding UnmatchedCount}" Width="100"/>
                                    <DataGridTextColumn Header="一致率" Binding="{Binding MatchRate}" Width="100"/>
                                </DataGrid.Columns>
                            </DataGrid>
                        </Grid>

                        <!-- 空状态提示 -->
                        <StackPanel Name="EmptyState"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center">
                            <materialDesign:PackIcon Kind="CompareHorizontal" 
                                                   Width="64" 
                                                   Height="64" 
                                                   Foreground="#FFCCCCCC"
                                                   Margin="0,0,0,15"/>
                            <TextBlock Text="月度を選択して照合を実行してください。"
                                       FontSize="16"
                                       Foreground="#FF999999"
                                       HorizontalAlignment="Center"/>
                        </StackPanel>

                        <!-- 处理进度指示器 -->
                        <Grid Name="ProcessingIndicator" 
                              Visibility="Collapsed">
                            <StackPanel HorizontalAlignment="Center" 
                                       VerticalAlignment="Center">
                                <ProgressBar IsIndeterminate="True" 
                                           Width="300" 
                                           Height="6"
                                           Margin="0,0,0,15"/>
                                <TextBlock Name="ProcessingText"
                                         Text="データ照合処理中..." 
                                         FontSize="16" 
                                         HorizontalAlignment="Center"
                                         Foreground="#FF2196F3"/>
                            </StackPanel>
                        </Grid>
                    </Grid>
                </ScrollViewer>
            </Grid>
        </Border>

        <!-- 底部操作区域 -->
        <Grid Grid.Row="4" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 状态信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="最終照合実行: " FontWeight="Medium"/>
                <TextBlock Name="LastReconciliationText" Text="未実行" Foreground="#FF666666"/>
                <TextBlock Text=" | 処理時間: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="ProcessingTimeText" Text="-" Foreground="#FF666666"/>
            </StackPanel>

            <!-- 导出按钮 -->
            <Button Grid.Column="1"
                    Name="ExportResultButton"
                    Content="結果エクスポート"
                    Style="{StaticResource ReconciliationButtonStyle}"
                    Background="#FF9C27B0"
                    IsEnabled="False"
                    Click="ExportResultButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="导出照合结果到Excel文件"/>
                </Button.ToolTip>
            </Button>
        </Grid>
    </Grid>
</UserControl>
