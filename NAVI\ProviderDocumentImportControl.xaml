<UserControl x:Class="NAVI.ProviderDocumentImportControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="ActionButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 处理结果列表样式 -->
        <Style x:Key="ResultListStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="IsReadOnly" Value="True"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="200"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="事業者紙資料取込" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF2196F3"
                   Margin="0,0,0,20"/>

        <!-- 操作按钮区域 -->
        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,20">
            <Button Name="SelectImageButton"
                    Content="選択文件"
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#FF2196F3"
                    Click="SelectImageButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="选择要处理的图像文件"/>
                </Button.ToolTip>
            </Button>

            <Button Name="OcrProcessButton"
                    Content="OCR認識実行"
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#FF4CAF50"
                    IsEnabled="False"
                    Click="OcrProcessButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="执行OCR文字识别"/>
                </Button.ToolTip>
            </Button>

            <Button Name="ClearButton"
                    Content="クリア"
                    Style="{StaticResource ActionButtonStyle}"
                    Background="#FFF44336"
                    Click="ClearButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="清除当前内容"/>
                </Button.ToolTip>
            </Button>
        </StackPanel>

        <!-- 图像预览区域标题 -->
        <TextBlock Grid.Row="2" 
                   Text="画像プレビュー:"
                   FontSize="16"
                   FontWeight="Medium"
                   Margin="0,0,0,10"/>

        <!-- 图像预览区域 -->
        <Border Grid.Row="3" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="2" 
                CornerRadius="8"
                Background="#FFF8F9FA">
            <Grid>
                <!-- 图像显示 -->
                <Image Name="PreviewImage" 
                       Stretch="Uniform"
                       Margin="10"
                       Visibility="Collapsed"/>

                <!-- 空状态提示 -->
                <StackPanel Name="EmptyImageState"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center">
                    <materialDesign:PackIcon Kind="ImageOutline" 
                                           Width="64" 
                                           Height="64" 
                                           Foreground="#FFCCCCCC"
                                           Margin="0,0,0,10"/>
                    <TextBlock Text="画像ファイルを選択してください。"
                               FontSize="16"
                               Foreground="#FF999999"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- 处理进度指示器 -->
                <Grid Name="ProcessingOverlay" 
                      Background="#80FFFFFF"
                      Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" 
                               VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" 
                                   Width="200" 
                                   Height="4"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="OCR処理中..." 
                                 FontSize="14" 
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 处理结果标题 -->
        <TextBlock Grid.Row="4" 
                   Text="処理済み画像一覧:"
                   FontSize="16"
                   FontWeight="Medium"
                   Margin="0,20,0,10"/>

        <!-- 处理结果列表 -->
        <Border Grid.Row="5" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="1" 
                CornerRadius="4"
                Background="White">
            <DataGrid Name="ProcessedImagesGrid"
                      Style="{StaticResource ResultListStyle}"
                      ItemsSource="{Binding ProcessedImages}"
                      SelectionChanged="ProcessedImagesGrid_SelectionChanged"
                      Margin="5">
                <DataGrid.Columns>
                    <DataGridTextColumn Header="ファイル名" 
                                      Binding="{Binding FileName}" 
                                      Width="300"/>
                    <DataGridTextColumn Header="処理時間" 
                                      Binding="{Binding ProcessTime}" 
                                      Width="200"/>
                    <DataGridTextColumn Header="認識状況" 
                                      Binding="{Binding RecognitionStatus}" 
                                      Width="200"/>
                    <DataGridTextColumn Header="成功レコード数" 
                  Binding="{Binding ImportCount}" 
                  Width="200"/>
                    <DataGridTextColumn Header="データ状態" 
                                      Binding="{Binding DataStatus}" 
                                      Width="200"/>
                    <DataGridTemplateColumn Header="操作" Width="150">
                        <DataGridTemplateColumn.CellTemplate>
                            <DataTemplate>
                                <StackPanel Orientation="Horizontal">
                                    <Button Content="詳細確認" 
                                            Background="#FF2196F3"
                                            Foreground="White"
                                            FontSize="11"
                                            Height="25"
                                            Width="90"
                                            Margin="2"
                                            Click="ViewDetailsButton_Click"
                                            Tag="{Binding}"/>
                                </StackPanel>
                            </DataTemplate>
                        </DataGridTemplateColumn.CellTemplate>
                    </DataGridTemplateColumn>
                </DataGrid.Columns>
            </DataGrid>
        </Border>
    </Grid>
</UserControl>
