using System;
using System.IO;
using NPOI.XSSF.UserModel;
using NPOI.SS.UserModel;

namespace NAVI.Services
{
    /// <summary>
    /// 事业者Excel测试文件创建器
    /// </summary>
    public static class ProviderExcelTestCreator
    {
        /// <summary>
        /// 创建测试用的事业者Excel文件
        /// </summary>
        public static void CreateTestProviderExcel(string filePath)
        {
            var workbook = new XSSFWorkbook();
            var sheet = workbook.CreateSheet("都加算組請求用");
            
            // 创建标题区域
            CreateTitleArea(sheet);
            
            // 创建基本信息区域
            CreateBasicInfoArea(sheet);
            
            // 创建服务明细区域
            CreateServiceDetailsArea(sheet);
            
            // 创建合计区域
            CreateTotalArea(sheet);
            
            // 保存文件
            Directory.CreateDirectory(Path.GetDirectoryName(filePath));
            using (var fileStream = new FileStream(filePath, FileMode.Create, FileAccess.Write))
            {
                workbook.Write(fileStream);
            }
            
            workbook.Close();
        }

        /// <summary>
        /// 创建标题区域
        /// </summary>
        private static void CreateTitleArea(ISheet sheet)
        {
            // 都加算組請求用 (行3, 列AA-AF)
            var titleRow = sheet.CreateRow(2);
            var titleCell = titleRow.CreateCell(26); // AA列
            titleCell.SetCellValue("都加算組請求用");
        }

        /// <summary>
        /// 创建基本信息区域
        /// </summary>
        private static void CreateBasicInfoArea(ISheet sheet)
        {
            // 都加算明細書 (行5)
            var headerRow = sheet.CreateRow(4);
            var headerCell = headerRow.CreateCell(12);
            headerCell.SetCellValue("都加算明細書");
            
            // 短期入所 (行6)
            var subHeaderRow = sheet.CreateRow(5);
            var subHeaderCell = subHeaderRow.CreateCell(15);
            subHeaderCell.SetCellValue("（短期入所）");
            
            // 年月信息 (行8, 列24-26)
            var dateRow = sheet.CreateRow(7);
            dateRow.CreateCell(23).SetCellValue("令和");
            dateRow.CreateCell(24).SetCellValue("7");
            dateRow.CreateCell(25).SetCellValue("年");
            dateRow.CreateCell(26).SetCellValue("2");
            dateRow.CreateCell(27).SetCellValue("月分");
            
            // 受给者证番号 (行10, 列25)
            var recipientRow1 = sheet.CreateRow(9);
            recipientRow1.CreateCell(8).SetCellValue("受給者証番号(10桁)");
            recipientRow1.CreateCell(24).SetCellValue("1234567890");
            
            // 事业所番号 (行10, 列32)
            recipientRow1.CreateCell(30).SetCellValue("事業所番号");
            recipientRow1.CreateCell(31).SetCellValue("1234567890");
            
            // 支给决定障害者氏名 (行11, 列25)
            var recipientRow2 = sheet.CreateRow(10);
            recipientRow2.CreateCell(8).SetCellValue("支給決定障害者");
            recipientRow2.CreateCell(24).SetCellValue("鈴木太郎");
            
            // 事业者及びその事业所の名称 (行11-12, 列32)
            recipientRow2.CreateCell(30).SetCellValue("事業者及");
            recipientRow2.CreateCell(31).SetCellValue("サポートハウス東京");
            
            var recipientRow3 = sheet.CreateRow(11);
            recipientRow3.CreateCell(8).SetCellValue("氏");
            recipientRow3.CreateCell(12).SetCellValue("名");
            recipientRow3.CreateCell(30).SetCellValue("びその事");
            
            // 支给决定に係る障害児氏名 (行13, 列25)
            var recipientRow4 = sheet.CreateRow(12);
            recipientRow4.CreateCell(8).SetCellValue("支給決定に係る");
            recipientRow4.CreateCell(24).SetCellValue("鈴木太郎");
            recipientRow4.CreateCell(30).SetCellValue("業所の名");
            
            var recipientRow5 = sheet.CreateRow(13);
            recipientRow5.CreateCell(8).SetCellValue("障害児氏名");
            recipientRow5.CreateCell(30).SetCellValue("称");
            
            // 障害支援区分 (行15, 列32)
            var supportRow = sheet.CreateRow(14);
            supportRow.CreateCell(8).SetCellValue("障害支援区分(障害児の障害の支援の区分)");
            supportRow.CreateCell(24).SetCellValue("6");
            supportRow.CreateCell(30).SetCellValue("地域区分");
            supportRow.CreateCell(31).SetCellValue("1級地");
            
            // 日期信息 (行18)
            var dateInfoRow = sheet.CreateRow(17);
            dateInfoRow.CreateCell(4).SetCellValue("開始年月日");
            dateInfoRow.CreateCell(7).SetCellValue("2025-2-1");
            dateInfoRow.CreateCell(12).SetCellValue("終了年月日");
            dateInfoRow.CreateCell(19).SetCellValue("2025-2-28");
            dateInfoRow.CreateCell(28).SetCellValue("利用日数");
            dateInfoRow.CreateCell(31).SetCellValue("28");
        }

        /// <summary>
        /// 创建服务明细区域
        /// </summary>
        private static void CreateServiceDetailsArea(ISheet sheet)
        {
            // 服务明细表头 (行20)
            var headerRow = sheet.CreateRow(19);
            headerRow.CreateCell(4).SetCellValue("サービス");
            headerRow.CreateCell(6).SetCellValue("コ");
            headerRow.CreateCell(8).SetCellValue("サービス内容");
            headerRow.CreateCell(16).SetCellValue("算定単価");
            headerRow.CreateCell(17).SetCellValue("利用日数");
            headerRow.CreateCell(18).SetCellValue("当月費定額");
            headerRow.CreateCell(20).SetCellValue("摘要");
            
            // 服务明细数据
            CreateServiceDetailRow(sheet, 20, "241111", "福祉短期入所Ⅰ６", "10210", "28", "285,880", "対応済み");
            CreateServiceDetailRow(sheet, 21, "241115", "福祉短期入所Ⅰ５・大規模減算", "10210", "12", "122,520", "未対応");
            CreateServiceDetailRow(sheet, 24, "246063", "短期医療連携体制加算Ⅰ１", "698", "", "", "");
            CreateServiceDetailRow(sheet, 25, "246063", "短期医療連携体制加算Ⅰ２", "436", "", "", "");
            CreateServiceDetailRow(sheet, 26, "246063", "短期医療連携体制加算Ⅰ３", "349", "", "", "");
            CreateServiceDetailRow(sheet, 27, "249919", "短期医療連携体制加算Ⅶ", "", "別途より", "", "3,950");
            CreateServiceDetailRow(sheet, 28, "246063", "短期医療連携体制加算Ⅷ", "790", "", "", "");
        }

        /// <summary>
        /// 创建单个服务明细行
        /// </summary>
        private static void CreateServiceDetailRow(ISheet sheet, int rowIndex, string serviceCode, string serviceContent, 
            string unitPrice, string usageDays, string monthlyAmount, string remarks)
        {
            var row = sheet.CreateRow(rowIndex);
            
            // 服务代码 (列B-D, 索引1-3)
            row.CreateCell(1).SetCellValue(serviceCode);
            
            // 服务内容 (列I-P, 索引8-15)
            row.CreateCell(8).SetCellValue(serviceContent);
            
            // 单定价格 (列Q, 索引16)
            if (!string.IsNullOrEmpty(unitPrice))
                row.CreateCell(16).SetCellValue(unitPrice);
            
            // 利用日数 (列R, 索引17)
            if (!string.IsNullOrEmpty(usageDays))
                row.CreateCell(17).SetCellValue(usageDays);
            
            // 当月费定额 (列S-T, 索引18-19)
            if (!string.IsNullOrEmpty(monthlyAmount))
                row.CreateCell(18).SetCellValue(monthlyAmount);
            
            // 摘要 (列U, 索引20)
            if (!string.IsNullOrEmpty(remarks))
                row.CreateCell(20).SetCellValue(remarks);
        }

        /// <summary>
        /// 创建合计区域
        /// </summary>
        private static void CreateTotalArea(ISheet sheet)
        {
            // 小计 (行25)
            var subtotalRow = sheet.CreateRow(24);
            subtotalRow.CreateCell(12).SetCellValue("小計");
            subtotalRow.CreateCell(18).SetCellValue("408,400");
            
            // 精神科医疗连携体制加算 (行31)
            var medicalRow = sheet.CreateRow(30);
            medicalRow.CreateCell(8).SetCellValue("精神科医療連携体制加算");
            medicalRow.CreateCell(16).SetCellValue("330");
            medicalRow.CreateCell(17).SetCellValue("40");
            medicalRow.CreateCell(18).SetCellValue("13,200");
            
            // 小计2 (行32)
            var subtotal2Row = sheet.CreateRow(31);
            subtotal2Row.CreateCell(12).SetCellValue("小計");
            subtotal2Row.CreateCell(18).SetCellValue("17,150");
            
            // 当月都加算请求额 (行34)
            var totalRow = sheet.CreateRow(33);
            totalRow.CreateCell(8).SetCellValue("当月都加算請求額 (①+②)");
            totalRow.CreateCell(18).SetCellValue("425,550");
            totalRow.CreateCell(32).SetCellValue("円");
        }
    }
}
