<UserControl x:Class="NAVI.DataEditControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
             mc:Ignorable="d"
             d:DesignHeight="600" d:DesignWidth="1000">

    <UserControl.Resources>
        <!-- 按钮样式 -->
        <Style x:Key="EditButtonStyle" TargetType="Button" BasedOn="{StaticResource MaterialDesignRaisedButton}">
            <Setter Property="Height" Value="35"/>
            <Setter Property="Margin" Value="5"/>
            <Setter Property="FontSize" Value="13"/>
            <Setter Property="FontWeight" Value="Medium"/>
        </Style>

        <!-- 数据类型选择样式 -->
        <Style x:Key="DataTypeComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Height" Value="40"/>
            <Setter Property="Width" Value="200"/>
            <Setter Property="Margin" Value="8"/>
            <Setter Property="FontSize" Value="14"/>
        </Style>

        <!-- 数据网格样式 -->
        <Style x:Key="EditDataGridStyle" TargetType="DataGrid" BasedOn="{StaticResource MaterialDesignDataGrid}">
            <Setter Property="AutoGenerateColumns" Value="False"/>
            <Setter Property="CanUserAddRows" Value="False"/>
            <Setter Property="CanUserDeleteRows" Value="False"/>
            <Setter Property="SelectionMode" Value="Single"/>
            <Setter Property="GridLinesVisibility" Value="Horizontal"/>
            <Setter Property="HeadersVisibility" Value="Column"/>
        </Style>
    </UserControl.Resources>

    <Grid Margin="20">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" 
                   Text="データ編集" 
                   FontSize="24" 
                   FontWeight="Bold" 
                   Foreground="#FF2196F3"
                   Margin="0,0,0,20"/>

        <!-- 数据类型选择和操作按钮 -->
        <Grid Grid.Row="1" Margin="0,0,0,20">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"/>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 数据类型选择 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="データ種別:" 
                           VerticalAlignment="Center" 
                           FontWeight="Medium" 
                           Margin="0,0,10,0"/>
                <ComboBox Name="DataTypeComboBox"
                          Style="{StaticResource DataTypeComboBoxStyle}"
                          SelectionChanged="DataTypeComboBox_SelectionChanged">
                    <ComboBoxItem Content="事業者管理データ" Tag="BusinessData"/>
                    <ComboBoxItem Content="国保連データ" Tag="NationalData"/>
                    <ComboBoxItem Content="サービスコードデータ" Tag="ServiceCodeData"/>
                    <ComboBoxItem Content="照合結果データ" Tag="ReconciliationData"/>
                </ComboBox>
            </StackPanel>

            <!-- 操作按钮 -->
            <StackPanel Grid.Column="2" Orientation="Horizontal">
                <Button Name="AddRecordButton"
                        Content="新規追加"
                        Style="{StaticResource EditButtonStyle}"
                        Background="#FF4CAF50"
                        Click="AddRecordButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="添加新记录"/>
                    </Button.ToolTip>
                </Button>

                <Button Name="EditRecordButton"
                        Content="編集"
                        Style="{StaticResource EditButtonStyle}"
                        Background="#FF2196F3"
                        IsEnabled="False"
                        Click="EditRecordButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="编辑选中的记录"/>
                    </Button.ToolTip>
                </Button>

                <Button Name="DeleteRecordButton"
                        Content="削除"
                        Style="{StaticResource EditButtonStyle}"
                        Background="#FFF44336"
                        IsEnabled="False"
                        Click="DeleteRecordButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="删除选中的记录"/>
                    </Button.ToolTip>
                </Button>

                <Button Name="SaveChangesButton"
                        Content="変更保存"
                        Style="{StaticResource EditButtonStyle}"
                        Background="#FFFF9800"
                        IsEnabled="False"
                        Click="SaveChangesButton_Click">
                    <Button.ToolTip>
                        <ToolTip Content="保存所有更改"/>
                    </Button.ToolTip>
                </Button>
            </StackPanel>
        </Grid>

        <!-- 搜索和筛选 -->
        <Grid Grid.Row="2" Margin="0,0,0,15">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 搜索框 -->
            <TextBox Grid.Column="0"
                     Name="SearchTextBox"
                     materialDesign:HintAssist.Hint="検索キーワードを入力..."
                     Height="40"
                     FontSize="14"
                     TextChanged="SearchTextBox_TextChanged"/>

            <!-- 搜索按钮 -->
            <Button Grid.Column="1"
                    Name="SearchButton"
                    Content="検索"
                    Style="{StaticResource EditButtonStyle}"
                    Background="#FF9C27B0"
                    Width="80"
                    Margin="10,0,0,0"
                    Click="SearchButton_Click"/>
        </Grid>

        <!-- 数据显示区域 -->
        <Border Grid.Row="3" 
                BorderBrush="#FFE0E0E0" 
                BorderThickness="1" 
                CornerRadius="4"
                Background="White">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="*"/>
                </Grid.RowDefinitions>

                <!-- 数据表标题 -->
                <Border Grid.Row="0" 
                        Background="#FFF5F5F5" 
                        BorderBrush="#FFE0E0E0" 
                        BorderThickness="0,0,0,1"
                        Padding="15">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*"/>
                            <ColumnDefinition Width="Auto"/>
                        </Grid.ColumnDefinitions>
                        
                        <TextBlock Grid.Column="0"
                                   Name="DataTableTitle"
                                   Text="データ一覧" 
                                   FontWeight="Medium" 
                                   FontSize="16"/>
                        
                        <StackPanel Grid.Column="1" Orientation="Horizontal">
                            <TextBlock Text="総件数: " FontWeight="Medium"/>
                            <TextBlock Name="TotalCountText" 
                                       Text="0" 
                                       Foreground="#FF2196F3"/>
                            <TextBlock Text=" | 選択: " FontWeight="Medium" Margin="15,0,0,0"/>
                            <TextBlock Name="SelectedCountText" 
                                       Text="0" 
                                       Foreground="#FF4CAF50"/>
                        </StackPanel>
                    </Grid>
                </Border>

                <!-- 数据网格 -->
                <DataGrid Grid.Row="1" 
                          Name="DataEditGrid"
                          Style="{StaticResource EditDataGridStyle}"
                          SelectionChanged="DataEditGrid_SelectionChanged"
                          Margin="5">
                    <!-- 动态生成列 -->
                </DataGrid>

                <!-- 空状态提示 -->
                <StackPanel Name="EmptyDataState"
                           Grid.Row="1"
                           HorizontalAlignment="Center"
                           VerticalAlignment="Center"
                           Visibility="Collapsed">
                    <materialDesign:PackIcon Kind="DatabaseOutline" 
                                           Width="64" 
                                           Height="64" 
                                           Foreground="#FFCCCCCC"
                                           Margin="0,0,0,15"/>
                    <TextBlock Text="データ種別を選択してください。"
                               FontSize="16"
                               Foreground="#FF999999"
                               HorizontalAlignment="Center"/>
                </StackPanel>

                <!-- 加载指示器 -->
                <Grid Name="LoadingIndicator" 
                      Grid.Row="1"
                      Background="#80FFFFFF"
                      Visibility="Collapsed">
                    <StackPanel HorizontalAlignment="Center" 
                               VerticalAlignment="Center">
                        <ProgressBar IsIndeterminate="True" 
                                   Width="200" 
                                   Height="4"
                                   Margin="0,0,0,10"/>
                        <TextBlock Text="データ読み込み中..." 
                                 FontSize="14" 
                                 HorizontalAlignment="Center"/>
                    </StackPanel>
                </Grid>
            </Grid>
        </Border>

        <!-- 底部状态栏 -->
        <Grid Grid.Row="4" Margin="0,15,0,0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"/>
                <ColumnDefinition Width="Auto"/>
            </Grid.ColumnDefinitions>

            <!-- 状态信息 -->
            <StackPanel Grid.Column="0" Orientation="Horizontal">
                <TextBlock Text="状態: " FontWeight="Medium"/>
                <TextBlock Name="StatusText" Text="データ種別選択待ち" Foreground="#FF666666"/>
                <TextBlock Text=" | 最終更新: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="LastUpdateText" Text="-" Foreground="#FF666666"/>
                <TextBlock Text=" | 未保存変更: " FontWeight="Medium" Margin="20,0,0,0"/>
                <TextBlock Name="UnsavedChangesText" Text="0" Foreground="#FFFF9800"/>
            </StackPanel>

            <!-- 刷新按钮 -->
            <Button Grid.Column="1"
                    Name="RefreshButton"
                    Content="更新"
                    Style="{StaticResource EditButtonStyle}"
                    Background="#FF607D8B"
                    Click="RefreshButton_Click">
                <Button.ToolTip>
                    <ToolTip Content="刷新数据"/>
                </Button.ToolTip>
            </Button>
        </Grid>
    </Grid>
</UserControl>
