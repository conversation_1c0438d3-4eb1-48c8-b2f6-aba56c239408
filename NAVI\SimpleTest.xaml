<Window x:Class="NAVI.SimpleTest"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:controls="clr-namespace:NAVI.Controls"
        Title="Simple Test" Height="200" Width="400">
    <Grid>
        <StackPanel Margin="20" VerticalAlignment="Center">
            <TextBlock Text="MonthYearPicker 简单测试" FontSize="16" FontWeight="Bold" Margin="0,0,0,20"/>
            
            <StackPanel Orientation="Horizontal" Margin="0,0,0,20">
                <TextBlock Text="选择年月：" VerticalAlignment="Center" Margin="0,0,10,0"/>
                <controls:MonthYearPicker x:Name="TestPicker" Width="200"/>
            </StackPanel>
            
            <Button Content="获取值" Click="GetValue_Click" Width="100"/>
            
            <TextBlock x:Name="ResultText" Text="结果显示在这里" Margin="0,10,0,0"/>
        </StackPanel>
    </Grid>
</Window>
