# ServiceCode CSV导入功能说明

## 概要
ServiceCodeDataControl窗体中的CSV导入功能已完成开发，支持将CSV文件中的服务代码数据导入到ServiceCodeMaster表中。

## 功能特点

### 1. 支持的CSV列格式
系统支持以下CSV列名（支持多种别名匹配）：

| 标准列名 | 支持的别名 | 是否必填 | 说明 |
|---------|-----------|---------|------|
| サービスコード | サービスコード, 服务代码, ServiceCode, Code | ✅ 必填 | 服务代码，不能为空 |
| サービス内容略称 | サービス内容略称, 服务内容略称, 服务内容, ServiceContent, Content | ❌ 可选 | 服务内容略称 |
| 障害支援区分 | 障害支援区分, 障害支援\r\n区分, 障害支援\n区分, 障害区分, DisabilityCategory | ❌ 可选 | 障害支援区分 |
| 合成単位 | 合成単位, 合成\r\n単位, 合成\n単位, 单位, Unit | ❌ 可选 | 合成单位 |
| 級地コード | 級地コード, 級地\r\nコード, 級地\nコード, 级地代码, RegionCode | ✅ 必填 | 级地代码，不能为空 |
| 単位数単価 | 単位数単価, 单位数单价, UnitPrice | ❌ 可选 | 单位数单价 |
| 国費単価 | 国費単価, 国費\r\n単価, 国費\n単価, 国费单价, NationalPrice | ❌ 可选 | 国费单价 |
| 旧身体療護 | 旧身体療護, 旧身体\r\n療護, 旧身体\n療護, 旧身体疗护, OldPhysicalCare | ❌ 可选 | 旧身体疗护 |
| 都単価 | 都単価, 都单价, CityPrice | ❌ 可选 | 都单价 |
| キーコード | キーコード, 键代码, KeyCode | ❌ 可选 | 键代码 |
| 都加算単価 | 都加算単価, 都加算\r\n単価, 都加算\n単価, 都加算单价, CityAdditionalPrice | ❌ 可选 | 都加算单价 |

### 2. 数据验证规则

#### 必填字段验证
- **サービスコード**: 不能为空
- **級地コード**: 不能为空

#### 重复性验证
- CSV文件内不能有重复的サービスコード
- 数据库中不能已存在相同的サービスコード

#### 数据格式验证
- 自动去除字段前后空格
- 支持换行符处理（\r\n, \n）

### 3. 导入流程

1. **文件选择**: 点击"CSV取込"按钮选择CSV文件
2. **文件读取**: 系统读取CSV文件并解析数据
3. **列名映射**: 自动匹配CSV列名到数据库字段
4. **数据验证**: 执行必填字段和重复性验证
5. **批量导入**: 将验证通过的数据导入到ServiceCodeMaster表
6. **结果反馈**: 显示导入成功或失败的详细信息

### 4. 错误处理

#### 文件格式错误
- 空文件或格式不正确
- 无法解析的CSV格式

#### 数据验证错误
- 必填字段为空
- CSV内重复数据
- 数据库中已存在相同记录

#### 导入错误
- 数据库连接失败
- 插入操作失败

## 使用方法

### 1. 准备CSV文件
创建包含服务代码数据的CSV文件，确保：
- 第一行为列标题
- 包含必填字段：サービスコード、級地コード
- 使用UTF-8编码保存

### 2. 执行导入
1. 在ServiceCodeDataControl界面点击"CSV取込"按钮
2. 选择准备好的CSV文件
3. 等待进度提示完成
4. 查看导入结果消息

### 3. 验证结果
导入完成后，可以在ServiceCodeDataControl的数据网格中查看导入的数据。

## 示例CSV文件

参考文件：`test_serviceCode_sample.csv`

```csv
サービスコード,サービス内容略称,障害支援区分,合成単位,級地コード,単位数単価,国費単価,旧身体療護,都単価,キーコード,都加算単価
241111,福祉短期入所Ⅰ６,地域区分1級地,障害支援区分6,1,1200,1100,旧身体療護1,1300,KEY001,1400
241112,福祉短期入所Ⅰ７,地域区分2級地,障害支援区分5,2,1150,1050,旧身体療護2,1250,KEY002,1350
```

## 技术实现

### 核心类
- `ServiceCodeCsvImportService`: CSV导入服务类
- `ServiceCodeRepository`: 数据访问层
- `ServiceCodeMaster`: 数据实体类

### 主要方法
- `ImportServiceCodeCsvAsync()`: 异步导入方法
- `MapCsvToServiceCodes()`: CSV数据映射
- `ValidateServiceCodes()`: 数据验证
- `ImportServiceCodesToDatabase()`: 数据库导入

## 注意事项

1. **编码格式**: 建议使用UTF-8编码保存CSV文件
2. **数据完整性**: 确保必填字段不为空
3. **重复检查**: 导入前会检查重复数据
4. **事务处理**: 导入过程中如有错误，已导入的数据不会回滚
5. **性能考虑**: 大文件导入时会显示进度提示

## 错误排查

### 常见问题
1. **"CSVファイルが空か、形式が正しくありません"**
   - 检查文件是否为空
   - 确认CSV格式正确

2. **"有効なサービスコードデータが見つかりません"**
   - 检查必填字段是否存在
   - 确认数据行不为空

3. **"データ検証に失敗しました"**
   - 查看详细错误信息
   - 检查重复数据或必填字段

4. **"サービスコード XXX は既にデータベースに存在します"**
   - 数据库中已存在相同的服务代码
   - 需要修改CSV中的重复数据
