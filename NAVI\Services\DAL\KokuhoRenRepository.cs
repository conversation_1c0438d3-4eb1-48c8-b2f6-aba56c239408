using System.Data.SQLite;
using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Threading.Tasks;

namespace NAVI.Services.DAL
{
    /// <summary>
    /// 国保联数据实体类
    /// </summary>
    public class KokuhoRenData
    {
        public int No { get; set; }
        public string サービス提供年月 { get; set; } = string.Empty;
        public string 請求年月日 { get; set; } = string.Empty;
        public string 請求回数 { get; set; } = string.Empty;
        public string 審査年月 { get; set; } = string.Empty;
        public string 事業者コード { get; set; } = string.Empty;
        public string 事業者名称 { get; set; } = string.Empty;
        public string 受給者番号 { get; set; } = string.Empty;
        public string 受給者名称 { get; set; } = string.Empty;
        public string 受給者名称カナ { get; set; } = string.Empty;
        public string 児童名称 { get; set; } = string.Empty;
        public string 児童名称カナ { get; set; } = string.Empty;
        public string 身体 { get; set; } = string.Empty;
        public string 知的 { get; set; } = string.Empty;
        public string 精神 { get; set; } = string.Empty;
        public string 難病 { get; set; } = string.Empty;
        public string 単価障害程度区分 { get; set; } = string.Empty;
        public string 障害支援区分 { get; set; } = string.Empty;
        public string サービスコード { get; set; } = string.Empty;
        public string サービス名称 { get; set; } = string.Empty;
        public string 算定時間 { get; set; } = string.Empty;
        public string 回数 { get; set; } = string.Empty;
        public string 算定時間x回数 { get; set; } = string.Empty;
        public string 単位数 { get; set; } = string.Empty;
        public string サービス単位 { get; set; } = string.Empty;
        public string 連合会審査区分名称 { get; set; } = string.Empty;
        public string 審査区分名称 { get; set; } = string.Empty;
        public string 返戻事由名称 { get; set; } = string.Empty;
        public string 判定フラグ { get; set; } = string.Empty;
        public string status { get; set; } = string.Empty;
    }

    /// <summary>
    /// 国保联数据访问类
    /// </summary>
    public class KokuhoRenRepository : BaseRepository<KokuhoRenData>
    {
        public KokuhoRenRepository(DatabaseService databaseService)
            : base(databaseService, "KokuhoRenData")
        {
        }

        /// <summary>
        /// 根据服务提供年月筛选数据（选择月份的1-25号数据）
        /// </summary>
        public async Task<List<KokuhoRenData>> GetByServiceMonthAsync(string serviceMonth)
        {
            try
            {
                // 根据选择的月份筛选数据，serviceMonth格式为"2025-01"
                // 构建日期范围查询（1-25号）
                var startDate = $"{serviceMonth}-01";
                var endDate = $"{serviceMonth}-29";

                // 筛选条件：請求年月日在指定月份的1-25号范围内
                var whereClause = "date(請求年月日) >= @startDate AND date(請求年月日) <= @endDate";

                return await GetByConditionAsync(whereClause,
                    CreateParameter("@startDate", startDate),
                    CreateParameter("@endDate", endDate));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据服务提供年月筛选数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据受给者番号获取数据
        /// </summary>
        public async Task<List<KokuhoRenData>> GetByRecipientNumberAsync(string recipientNumber)
        {
            try
            {
                return await GetByConditionAsync("受給者番号 = @recipientNumber",
                    CreateParameter("@recipientNumber", recipientNumber));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据受给者番号获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据事业者代码获取数据
        /// </summary>
        public async Task<List<KokuhoRenData>> GetByProviderCodeAsync(string providerCode)
        {
            try
            {
                return await GetByConditionAsync("事業者コード = @providerCode",
                    CreateParameter("@providerCode", providerCode));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据事业者代码获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 根据状态获取数据
        /// </summary>
        public async Task<List<KokuhoRenData>> GetByStatusAsync(string status)
        {
            try
            {
                return await GetByConditionAsync("status = @status",
                    CreateParameter("@status", status));
            }
            catch (Exception ex)
            {
                throw new Exception($"根据状态获取数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新状态
        /// </summary>
        public async Task<int> UpdateStatusAsync(int no, string status)
        {
            try
            {
                var data = new KokuhoRenData { status = status };
                return await UpdateAsync(data, "No = @no", CreateParameter("@no", no));
            }
            catch (Exception ex)
            {
                throw new Exception($"更新状态失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 批量更新状态
        /// </summary>
        public async Task<int> BatchUpdateStatusAsync(List<int> nos, string status)
        {
            try
            {
                var updatedCount = 0;
                foreach (var no in nos)
                {
                    updatedCount += await UpdateStatusAsync(no, status);
                }
                return updatedCount;
            }
            catch (Exception ex)
            {
                throw new Exception($"批量更新状态失败: {ex.Message}", ex);
            }
        }

        protected override List<KokuhoRenData> ConvertDataTableToList(DataTable dataTable)
        {
            var dataList = new List<KokuhoRenData>();
            foreach (DataRow row in dataTable.Rows)
            {
                dataList.Add(new KokuhoRenData
                {
                    No = GetIntValue(row, "No"),
                    サービス提供年月 = GetStringValue(row, "サービス提供年月"),
                    請求年月日 = GetStringValue(row, "請求年月日"),
                    請求回数 = GetStringValue(row, "請求回数"),
                    審査年月 = GetStringValue(row, "審査年月"),
                    事業者コード = GetStringValue(row, "事業者コード"),
                    事業者名称 = GetStringValue(row, "事業者名称"),
                    受給者番号 = GetStringValue(row, "受給者番号"),
                    受給者名称 = GetStringValue(row, "受給者名称"),
                    受給者名称カナ = GetStringValue(row, "受給者名称カナ"),
                    児童名称 = GetStringValue(row, "児童名称"),
                    児童名称カナ = GetStringValue(row, "児童名称カナ"),
                    身体 = GetStringValue(row, "身体"),
                    知的 = GetStringValue(row, "知的"),
                    精神 = GetStringValue(row, "精神"),
                    難病 = GetStringValue(row, "難病"),
                    単価障害程度区分 = GetStringValue(row, "単価障害程度区分"),
                    障害支援区分 = GetStringValue(row, "障害支援区分"),
                    サービスコード = GetStringValue(row, "サービスコード"),
                    サービス名称 = GetStringValue(row, "サービス名称"),
                    算定時間 = GetStringValue(row, "算定時間"),
                    回数 = GetStringValue(row, "回数"),
                    算定時間x回数 = GetStringValue(row, "算定時間×回数"),
                    単位数 = GetStringValue(row, "単位数"),
                    サービス単位 = GetStringValue(row, "サービス単位"),
                    連合会審査区分名称 = GetStringValue(row, "連合会審査区分名称"),
                    審査区分名称 = GetStringValue(row, "審査区分名称"),
                    返戻事由名称 = GetStringValue(row, "返戻事由名称"),
                    判定フラグ = GetStringValue(row, "判定フラグ"),
                    status = GetStringValue(row, "status")
                });
            }
            return dataList;
        }

        protected override DataTable ConvertListToDataTable(List<KokuhoRenData> entities)
        {
            var dataTable = new DataTable();
            var columns = GetColumnNames();

            foreach (var column in columns)
            {
                dataTable.Columns.Add(column);
            }

            foreach (var data in entities)
            {
                var row = dataTable.NewRow();
                row["No"] = data.No;
                row["サービス提供年月"] = data.サービス提供年月;
                row["請求年月日"] = data.請求年月日;
                row["請求回数"] = data.請求回数;
                row["審査年月"] = data.審査年月;
                row["事業者コード"] = data.事業者コード;
                row["事業者名称"] = data.事業者名称;
                row["受給者番号"] = data.受給者番号;
                row["受給者名称"] = data.受給者名称;
                row["受給者名称カナ"] = data.受給者名称カナ;
                row["児童名称"] = data.児童名称;
                row["児童名称カナ"] = data.児童名称カナ;
                row["身体"] = data.身体;
                row["知的"] = data.知的;
                row["精神"] = data.精神;
                row["難病"] = data.難病;
                row["単価障害程度区分"] = data.単価障害程度区分;
                row["障害支援区分"] = data.障害支援区分;
                row["サービスコード"] = data.サービスコード;
                row["サービス名称"] = data.サービス名称;
                row["算定時間"] = data.算定時間;
                row["回数"] = data.回数;
                row["算定時間x回数"] = data.算定時間x回数;
                row["単位数"] = data.単位数;
                row["サービス単位"] = data.サービス単位;
                row["連合会審査区分名称"] = data.連合会審査区分名称;
                row["審査区分名称"] = data.審査区分名称;
                row["返戻事由名称"] = data.返戻事由名称;
                row["判定フラグ"] = data.判定フラグ;
                row["status"] = data.status;
                dataTable.Rows.Add(row);
            }

            return dataTable;
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildInsertCommand(KokuhoRenData entity)
        {
            var sql = @"INSERT INTO KokuhoRenData 
                (サービス提供年月, 請求年月日, 請求回数, 審査年月, 事業者コード, 事業者名称, 受給者番号, 受給者名称, 受給者名称カナ, 児童名称, 児童名称カナ, 身体, 知的, 精神, 難病, 単価障害程度区分, 障害支援区分, サービスコード, サービス名称, 算定時間, 回数, 算定時間×回数, 単位数, サービス単位, 連合会審査区分名称, 審査区分名称, 返戻事由名称, 判定フラグ, status) 
                VALUES (@サービス提供年月, @請求年月日, @請求回数, @審査年月, @事業者コード, @事業者名称, @受給者番号, @受給者名称, @受給者名称カナ, @児童名称, @児童名称カナ, @身体, @知的, @精神, @難病, @単価障害程度区分, @障害支援区分, @サービスコード, @サービス名称, @算定時間, @回数, @算定時間x回数, @単位数, @サービス単位, @連合会審査区分名称, @審査区分名称, @返戻事由名称, @判定フラグ, @status)";

            var parameters = new[]
            {
                CreateParameter("@サービス提供年月", entity.サービス提供年月),
                CreateParameter("@請求年月日", entity.請求年月日),
                CreateParameter("@請求回数", entity.請求回数),
                CreateParameter("@審査年月", entity.審査年月),
                CreateParameter("@事業者コード", entity.事業者コード),
                CreateParameter("@事業者名称", entity.事業者名称),
                CreateParameter("@受給者番号", entity.受給者番号),
                CreateParameter("@受給者名称", entity.受給者名称),
                CreateParameter("@受給者名称カナ", entity.受給者名称カナ),
                CreateParameter("@児童名称", entity.児童名称),
                CreateParameter("@児童名称カナ", entity.児童名称カナ),
                CreateParameter("@身体", entity.身体),
                CreateParameter("@知的", entity.知的),
                CreateParameter("@精神", entity.精神),
                CreateParameter("@難病", entity.難病),
                CreateParameter("@単価障害程度区分", entity.単価障害程度区分),
                CreateParameter("@障害支援区分", entity.障害支援区分),
                CreateParameter("@サービスコード", entity.サービスコード),
                CreateParameter("@サービス名称", entity.サービス名称),
                CreateParameter("@算定時間", entity.算定時間),
                CreateParameter("@回数", entity.回数),
                CreateParameter("@算定時間x回数", entity.算定時間x回数),
                CreateParameter("@単位数", entity.単位数),
                CreateParameter("@サービス単位", entity.サービス単位),
                CreateParameter("@連合会審査区分名称", entity.連合会審査区分名称),
                CreateParameter("@審査区分名称", entity.審査区分名称),
                CreateParameter("@返戻事由名称", entity.返戻事由名称),
                CreateParameter("@判定フラグ", entity.判定フラグ),
                CreateParameter("@status", entity.status)
            };

            return (sql, parameters);
        }

        protected override (string sql, SQLiteParameter[] parameters) BuildUpdateCommand(KokuhoRenData entity, string whereClause, SQLiteParameter[] whereParameters)
        {
            var setParts = new List<string>();
            var parameters = new List<SQLiteParameter>();

            if (!string.IsNullOrEmpty(entity.status))
            {
                setParts.Add("status = @status");
                parameters.Add(CreateParameter("@status", entity.status));
            }

            if (whereParameters != null)
            {
                parameters.AddRange(whereParameters);
            }

            var sql = $"UPDATE KokuhoRenData SET {string.Join(", ", setParts)} WHERE {whereClause}";
            return (sql, parameters.ToArray());
        }

        protected override List<string> GetColumnNames()
        {
            return new List<string>
            {
                "No", "サービス提供年月", "請求年月日", "請求回数", "審査年月", "事業者コード", "事業者名称", "受給者番号", "受給者名称", "受給者名称カナ", "児童名称", "児童名称カナ", "身体", "知的", "精神", "難病", "単価障害程度区分", "障害支援区分", "サービスコード", "サービス名称", "算定時間", "回数", "算定時間x回数", "単位数", "サービス単位", "連合会審査区分名称", "審査区分名称", "返戻事由名称", "判定フラグ", "status"
            };
        }


        public List<string> GetColumnNamesC()
        {
            return new List<string>
            {
                "No", "サービス提供年月", "請求年月日", "請求回数", "審査年月", "事業者コード", "事業者名称", "受給者番号", "受給者名称", "受給者名称カナ", "児童名称", "児童名称カナ", "身体", "知的", "精神", "難病", "単価障害程度区分", "障害支援区分", "サービスコード", "サービス名称", "算定時間", "回数", "算定時間x回数", "単位数", "サービス単位", "連合会審査区分名称", "審査区分名称", "返戻事由名称", "判定フラグ", "status"
            };
        }

        /// <summary>
        /// 创建国保联数据
        /// </summary>
        public async Task<KokuhoRenData> CreateKokuhoRenDataAsync(KokuhoRenData entity)
        {
            try
            {
                var insertedId = await InsertAsync(entity);
                entity.No = (int)insertedId;
                return entity;
            }
            catch (Exception ex)
            {
                throw new Exception($"创建国保联数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 更新国保联数据
        /// </summary>
        public async Task<bool> UpdateKokuhoRenDataAsync(KokuhoRenData entity)
        {
            try
            {
                var result = await UpdateAsync(entity, "No = @no", CreateParameter("@no", entity.No));
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"更新国保联数据失败: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// 删除国保联数据
        /// </summary>
        public async Task<bool> DeleteKokuhoRenDataAsync(int no)
        {
            try
            {
                var result = await DeleteAsync("No = @no", CreateParameter("@no", no));
                return result > 0;
            }
            catch (Exception ex)
            {
                throw new Exception($"删除国保联数据失败: {ex.Message}", ex);
            }
        }
    }
}
