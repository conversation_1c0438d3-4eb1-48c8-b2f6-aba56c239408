using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Windows;

namespace NAVI
{
    /// <summary>
    /// RecipientInfoDataControl.xaml の交互逻辑
    /// 受給者マスタデータ管理
    /// </summary>
    public partial class RecipientInfoDataControl : UserControl
    {
        private ObservableCollection<RecipientInfo> _recipientInfoList;
        private List<RecipientInfo> _allData;
        private System.Windows.Threading.DispatcherTimer _searchTimer;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public RecipientInfoDataControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 加载示例数据
                LoadSampleData();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 加载示例数据
        /// </summary>
        private void LoadSampleData()
        {
            _allData = RecipientInfo.GetSampleData();
            _totalRecords = _allData.Count;
            ApplyPagination();
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            if (_allData == null || !_allData.Any()) return;

            var pagedData = _allData
                .Skip((_currentPage - 1) * _pageSize)
                .Take(_pageSize)
                .ToList();

            _recipientInfoList = new ObservableCollection<RecipientInfo>(pagedData);
            RecipientInfoGrid.ItemsSource = _recipientInfoList;

            UpdatePagination();
            UpdateStatusInfo();
        }

        /// <summary>
        /// 更新分页控件
        /// </summary>
        private void UpdatePagination()
        {
            if (PaginationControl != null)
            {
                var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
                PaginationControl.CurrentPage = _currentPage;
                PaginationControl.TotalPages = Math.Max(1, totalPages);
                PaginationControl.TotalRecords = _totalRecords;
                PaginationControl.PageSize = _pageSize;
            }
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            if (StatusTextBlock != null)
            {
                var startRecord = (_currentPage - 1) * _pageSize + 1;
                var endRecord = Math.Min(_currentPage * _pageSize, _totalRecords);
                StatusTextBlock.Text = $"全{_totalRecords}件中 {startRecord}-{endRecord}件を表示";
            }
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 设置分页控件事件
            if (PaginationControl != null)
            {
                PaginationControl.PageChanged += (sender, e) =>
                {
                    _currentPage = e.NewPage;
                    ApplyPagination();
                };
            }

            // 设置搜索延迟定时器
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (sender, e) =>
            {
                _searchTimer.Stop();
                PerformSearch();
            };

            // 搜索框文本变化事件
            SearchTextBox.TextChanged += (sender, e) =>
            {
                _searchTimer.Stop();
                _searchTimer.Start();
            };
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            var placeholderText = "受給者番号・氏名・其他关键字";
            
            SearchTextBox.GotFocus += (sender, e) =>
            {
                if (SearchTextBox.Text == placeholderText)
                {
                    SearchTextBox.Text = "";
                    SearchTextBox.Foreground = System.Windows.Media.Brushes.Black;
                }
            };

            SearchTextBox.LostFocus += (sender, e) =>
            {
                if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
                {
                    SearchTextBox.Text = placeholderText;
                    SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
                }
            };

            // 初始设置
            SearchTextBox.Text = placeholderText;
            SearchTextBox.Foreground = System.Windows.Media.Brushes.Gray;
        }

        /// <summary>
        /// 执行搜索
        /// </summary>
        private void PerformSearch()
        {
            try
            {
                var searchText = SearchTextBox.Text?.Trim();
                if (string.IsNullOrEmpty(searchText) || searchText == "受給者番号・氏名・其他关键字")
                {
                    _allData = RecipientInfo.GetSampleData();
                }
                else
                {
                    var allSampleData = RecipientInfo.GetSampleData();
                    _allData = allSampleData.Where(item =>
                        item.受給者番号.ToLower().Contains(searchText.ToLower()) ||
                        item.支給決定障害者氏名.ToLower().Contains(searchText.ToLower()) ||
                        item.支給決定に係る障害児氏名.ToLower().Contains(searchText.ToLower()) ||
                        item.改名後氏名.ToLower().Contains(searchText.ToLower())
                    ).ToList();
                }

                _totalRecords = _allData.Count;
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"検索に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var columnNames = new List<string> { "受給者番号", "支給決定障害者氏名", "支給決定に係る障害児氏名", "改名後氏名" };
                var editWindow = new EditWindow("新規受給者追加", columnNames);
                if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                {
                    var newRecipient = new RecipientInfo
                    {
                        受給者番号 = editWindow.ResultData.ContainsKey("受給者番号") ? editWindow.ResultData["受給者番号"]?.ToString() ?? "" : "",
                        支給決定障害者氏名 = editWindow.ResultData.ContainsKey("支給決定障害者氏名") ? editWindow.ResultData["支給決定障害者氏名"]?.ToString() ?? "" : "",
                        支給決定に係る障害児氏名 = editWindow.ResultData.ContainsKey("支給決定に係る障害児氏名") ? editWindow.ResultData["支給決定に係る障害児氏名"]?.ToString() ?? "" : "",
                        改名後氏名 = editWindow.ResultData.ContainsKey("改名後氏名") ? editWindow.ResultData["改名後氏名"]?.ToString() ?? "" : ""
                    };

                    _allData.Add(newRecipient);
                    _totalRecords = _allData.Count;
                    ApplyPagination();
                    MessageBox.Show("データの追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private void EditButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is RecipientInfo selectedItem)
                {
                    var columnNames = new List<string> { "受給者番号", "支給決定障害者氏名", "支給決定に係る障害児氏名", "改名後氏名" };
                    var editData = new Dictionary<string, object>
                    {
                        ["受給者番号"] = selectedItem.受給者番号,
                        ["支給決定障害者氏名"] = selectedItem.支給決定障害者氏名,
                        ["支給決定に係る障害児氏名"] = selectedItem.支給決定に係る障害児氏名,
                        ["改名後氏名"] = selectedItem.改名後氏名
                    };

                    var editWindow = new EditWindow("受給者データ編集", columnNames, editData);
                    if (editWindow.ShowDialog() == true && editWindow.IsSaved)
                    {
                        selectedItem.受給者番号 = editWindow.ResultData.ContainsKey("受給者番号") ? editWindow.ResultData["受給者番号"]?.ToString() ?? "" : "";
                        selectedItem.支給決定障害者氏名 = editWindow.ResultData.ContainsKey("支給決定障害者氏名") ? editWindow.ResultData["支給決定障害者氏名"]?.ToString() ?? "" : "";
                        selectedItem.支給決定に係る障害児氏名 = editWindow.ResultData.ContainsKey("支給決定に係る障害児氏名") ? editWindow.ResultData["支給決定に係る障害児氏名"]?.ToString() ?? "" : "";
                        selectedItem.改名後氏名 = editWindow.ResultData.ContainsKey("改名後氏名") ? editWindow.ResultData["改名後氏名"]?.ToString() ?? "" : "";

                        MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (sender is Button button && button.Tag is RecipientInfo selectedItem)
                {
                    var result = MessageBox.Show(
                        $"受給者番号「{selectedItem.受給者番号}」のデータを削除しますか？",
                        "削除確認",
                        MessageBoxButton.YesNo,
                        MessageBoxImage.Question);

                    if (result == MessageBoxResult.Yes)
                    {
                        _allData.Remove(selectedItem);
                        _totalRecords = _allData.Count;
                        
                        // 如果当前页没有数据了，回到上一页
                        var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);
                        if (_currentPage > totalPages && totalPages > 0)
                        {
                            _currentPage = totalPages;
                        }
                        
                        ApplyPagination();
                        MessageBox.Show("データの削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            PerformSearch();
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                InitializeData();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
