using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Media;
using Microsoft.Win32;
using NAVI.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Services.DAL;
using NAVI.Windows;
using NAVI.Utils;
using System.Data.SQLite;

namespace NAVI
{
    /// <summary>
    /// UserManagementControl.xaml 的交互逻辑
    /// </summary>
    public partial class UserManagementControl : UserControl
    {
        private ObservableCollection<UserData> _userDataList;
        private List<UserData> _allUsers;
        private List<string> _columnNames;
        private DatabaseManager _databaseManager;
        private UserRepository _userRepository;
        private System.Windows.Threading.DispatcherTimer _searchTimer;

        // 分页相关
        private int _currentPage = 1;
        private int _pageSize = 10;
        private int _totalRecords = 0;

        public UserManagementControl()
        {
            InitializeComponent();
            InitializeData();
            SetupEventHandlers();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            try
            {
                // 初始化数据库管理器
                _databaseManager = new DatabaseManager();
                _userRepository = _databaseManager.Users;

                // 从数据库加载数据
                LoadDataFromDatabase();

                // 设置搜索框的占位符效果
                SetupSearchBoxPlaceholder();

                // 初始化分页
                UpdatePagination();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ初期化に失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 从数据库加载数据（同步版本，保持兼容性）
        /// </summary>
        private void LoadDataFromDatabase()
        {
            try
            {
                // 设置列名
                _columnNames = new List<string>
                {
                    "職員番号", "部署名", "役職", "氏名", "ID番号", "パスワード", "作成日時", "更新日時"
                };

                CreateDynamicColumns();

                // 使用异步加载
                Task.Run(async () => await LoadPagedDataAsync());
            }
            catch (Exception ex)
            {
                throw new Exception($"从数据库加载数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private async Task LoadPagedDataAsync()
        {
            try
            {
                string searchText = null;

                Dispatcher.Invoke(() =>
                {
                    searchText = SearchTextBox?.Text?.Trim();
                });
                string whereClause = "";
                var parameters = new List<SQLiteParameter>();

                // 构建搜索条件
                if (!string.IsNullOrEmpty(searchText) && searchText != "職員番号・氏名・役職で検索")
                {
                    whereClause = @"""職員番号"" LIKE @search OR ""氏名"" LIKE @search OR ""部署名"" LIKE @search OR ""役職"" LIKE @search";
                    parameters.Add(new SQLiteParameter("@search", $"%{searchText}%"));
                }

                // 使用分页查询
                var (users, totalCount) = await _userRepository.GetPagedAsync(
                    _currentPage, _pageSize, whereClause, parameters.ToArray());

                _totalRecords = totalCount;

                // 转换数据
                var userDataList = ConvertUsersToUserData(users);
                _userDataList = new ObservableCollection<UserData>(userDataList);

                // 更新UI（需要在UI线程中执行）
                Application.Current.Dispatcher.Invoke(() =>
                {
                    UserDataGrid.ItemsSource = _userDataList;
                    UpdatePagination();
                    UpdateStatusInfo();
                });
            }
            catch (Exception ex)
            {
                throw new Exception($"加载分页数据失败：{ex.Message}");
            }
        }

        /// <summary>
        /// 将User转换为UserData
        /// </summary>
        private List<UserData> ConvertUsersToUserData(List<User> users)
        {
            var userDataList = new List<UserData>();

            foreach (var user in users)
            {
                var userData = new UserData();
                userData.SetProperty("職員番号", user.職員番号);
                userData.SetProperty("部署名", user.部署名);
                userData.SetProperty("役職", user.役職);
                userData.SetProperty("氏名", user.氏名);
                userData.SetProperty("ID番号", user.ID番号);
                userData.SetProperty("パスワード", user.パスワード);
                userData.SetProperty("作成日時", user.作成日時);
                userData.SetProperty("更新日時", user.更新日時);

                userDataList.Add(userData);
            }

            return userDataList;
        }

        /// <summary>
        /// 创建动态列
        /// </summary>
        private void CreateDynamicColumns()
        {
            // 清除除操作列外的所有列
            var operationColumn = UserDataGrid.Columns.FirstOrDefault();
            UserDataGrid.Columns.Clear();
            if (operationColumn != null)
            {
                UserDataGrid.Columns.Add(operationColumn);
            }

            // 添加动态列
            foreach (var columnName in _columnNames)
            {
                var column = new DataGridTextColumn
                {
                    Header = columnName,
                    Binding = new System.Windows.Data.Binding(columnName),
                    Width = GetColumnWidth(columnName)
                };
                UserDataGrid.Columns.Add(column);
            }
        }

        /// <summary>
        /// 获取列宽度
        /// </summary>
        private DataGridLength GetColumnWidth(string columnName)
        {
            // 根据列名设置合适的宽度
            if (columnName.Contains("番号") || columnName.Contains("No"))
                return new DataGridLength(80);
            else if (columnName.Contains("氏名") || columnName.Contains("名称"))
                return new DataGridLength(100);
            else if (columnName.Contains("部署") || columnName.Contains("役職"))
                return new DataGridLength(120);
            else if (columnName.Contains("パスワード"))
                return new DataGridLength(100);
            else if (columnName.Contains("日時"))
                return new DataGridLength(150);
            else
                return new DataGridLength(120);
        }

        /// <summary>
        /// 设置事件处理器
        /// </summary>
        private void SetupEventHandlers()
        {
            // 分页控件事件
            PaginationControl.PageChanged += PaginationControl_PageChanged;
            PaginationControl.PageSizeChanged += PaginationControl_PageSizeChanged;

            // DataGrid选择变化事件
            UserDataGrid.SelectionChanged += UserDataGrid_SelectionChanged;

            // 搜索框事件
            SearchTextBox.GotFocus += SearchTextBox_GotFocus;
            SearchTextBox.LostFocus += SearchTextBox_LostFocus;
            SearchTextBox.TextChanged += SearchTextBox_TextChanged;
        }

        /// <summary>
        /// 应用分页
        /// </summary>
        private void ApplyPagination()
        {
            // 使用数据库分页查询
            Task.Run(async () => await LoadPagedDataAsync());

            UpdatePagination();
        }

        /// <summary>
        /// 更新分页信息
        /// </summary>
        private void UpdatePagination()
        {
            var totalPages = (int)Math.Ceiling((double)_totalRecords / _pageSize);

            PaginationControl.CurrentPage = _currentPage;
            PaginationControl.TotalPages = Math.Max(1, totalPages);
            PaginationControl.TotalRecords = _totalRecords;
            PaginationControl.PageSize = _pageSize;
        }

        /// <summary>
        /// 设置搜索框占位符效果
        /// </summary>
        private void SetupSearchBoxPlaceholder()
        {
            SearchTextBox.Foreground = Brushes.Gray;
        }

        /// <summary>
        /// 搜索框获得焦点事件
        /// </summary>
        private void SearchTextBox_GotFocus(object sender, RoutedEventArgs e)
        {
            if (SearchTextBox.Text == "職員番号・氏名・部署名等のキーワードを入力")
            {
                SearchTextBox.Text = "";
                SearchTextBox.Foreground = Brushes.Black;
            }
        }

        /// <summary>
        /// 搜索框失去焦点事件
        /// </summary>
        private void SearchTextBox_LostFocus(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(SearchTextBox.Text))
            {
                SearchTextBox.Text = "職員番号・氏名・部署名等のキーワードを入力";
                SearchTextBox.Foreground = Brushes.Gray;
            }
        }

        /// <summary>
        /// 搜索框文本变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 延迟搜索，避免频繁查询
            if (_searchTimer != null)
            {
                _searchTimer.Stop();
            }
            _searchTimer = new System.Windows.Threading.DispatcherTimer
            {
                Interval = TimeSpan.FromMilliseconds(500)
            };
            _searchTimer.Tick += (s, args) =>
            {
                _searchTimer.Stop();
                _currentPage = 1; // 重置到第一页
                ApplyPagination();
            };
            _searchTimer.Start();
        }

        /// <summary>
        /// DataGrid选择变化事件
        /// </summary>
        private void UserDataGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            UpdateStatusInfo();
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            int selectedCount = UserDataGrid.SelectedItems.Count;

            // 尝试更新主窗口的状态栏
            var mainWindow = Application.Current.MainWindow as MainWindow;
            mainWindow?.UpdateStatusBar(_totalRecords, selectedCount, PaginationControl.TotalPages, "");
        }

        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, PageChangedEventArgs e)
        {
            _currentPage = e.NewPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, PageSizeChangedEventArgs e)
        {
            _pageSize = e.NewPageSize;
            _currentPage = 1; // 重置到第一页
            ApplyPagination();
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            _currentPage = 1;
            ApplyPagination();
        }
        /// <summary>
        /// 新增按钮点击事件
        /// </summary>
        private async void AddButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var editWindow = new UserEditWindow("新規職員追加");
                if (editWindow.ShowDialog() == true)
                {
                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("職員の追加が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"職員の追加に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// CSV导入按钮点击事件
        /// </summary>
        private async void ImportCsvButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var openFileDialog = new OpenFileDialog
                {
                    Title = "选择用户CSV文件",
                    Filter = "CSV文件 (*.csv)|*.csv|所有文件 (*.*)|*.*",
                    FilterIndex = 1
                };

                if (openFileDialog.ShowDialog() == true)
                {
                    var csvImportService = new CsvImportService();

                    // 显示进度提示
                    var progressWindow = new ProgressWindow("正在导入CSV数据...");
                    progressWindow.Show();

                    try
                    {
                        var result = await csvImportService.ImportUserCsvAsync(openFileDialog.FileName);

                        progressWindow.Close();

                        if (result.IsSuccess)
                        {
                            LoadDataFromDatabase();
                            MessageBox.Show($"CSV取込が完了しました！\n{result.SuccessMessage}", "取込成功",
                                MessageBoxButton.OK, MessageBoxImage.Information);
                        }
                        else
                        {
                            var errorMessage = result.ErrorMessage;
                            if (result.ValidationErrors.Any())
                            {
                                errorMessage += "\n\n詳細エラー：\n" + string.Join("\n", result.ValidationErrors);
                            }
                            MessageBox.Show(errorMessage, "取込失敗", MessageBoxButton.OK, MessageBoxImage.Error);
                        }
                    }
                    finally
                    {
                        if (progressWindow.IsVisible)
                            progressWindow.Close();
                        csvImportService.Dispose();
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"CSV取込に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 编辑按钮点击事件
        /// </summary>
        private async void EditButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedUser = GetSelectedUser(sender);
            if (selectedUser == null)
            {
                MessageBox.Show("編集する職員を選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            try
            {
                var editWindow = new UserEditWindow("職員編集", selectedUser);
                if (editWindow.ShowDialog() == true)
                {
                    // 刷新数据
                    LoadDataFromDatabase();
                    MessageBox.Show("職員情報の更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"職員編集に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 删除按钮点击事件
        /// </summary>
        private async void DeleteButton_Click(object sender, RoutedEventArgs e)
        {
            var selectedUser = GetSelectedUser(sender);
            if (selectedUser == null)
            {
                MessageBox.Show("削除する職員を選択してください！", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show($"職員 {selectedUser.氏名} を削除してもよろしいですか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                try
                {
                    await _userRepository.DeleteAsync("\"職員番号\" = @employeeNumber",
                        _userRepository.CreateParameter("@employeeNumber", selectedUser.職員番号));

                    LoadDataFromDatabase();
                    MessageBox.Show("職員の削除が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
                }
                catch (Exception ex)
                {
                    MessageBox.Show($"職員削除に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        /// <summary>
        /// 获取选中的用户
        /// </summary>
        private UserData GetSelectedUser(object sender)
        {
            if (sender is Button button && button.Tag is UserData user)
                return user;
            
            return UserDataGrid.SelectedItem as UserData;
        }

        /// <summary>
        /// 导出按钮点击事件
        /// </summary>
        private void ExportButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("エクスポート機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 打印按钮点击事件
        /// </summary>
        private void PrintButton_Click(object sender, RoutedEventArgs e)
        {
            MessageBox.Show("印刷機能は開発中です...", "お知らせ", MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                SearchTextBox.Text = "";
                LoadDataFromDatabase();
                MessageBox.Show("データの更新が完了しました！", "成功", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ更新に失敗しました：{ex.Message}", "エラー", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }



        /// <summary>
        /// 分页控件页码变化事件
        /// </summary>
        private void PaginationControl_PageChanged(object sender, int newPage)
        {
            _currentPage = newPage;
            ApplyPagination();
        }

        /// <summary>
        /// 分页控件页面大小变化事件
        /// </summary>
        private void PaginationControl_PageSizeChanged(object sender, int newPageSize)
        {
            _pageSize = newPageSize;
            _currentPage = 1;
            ApplyPagination();
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            _databaseManager?.Dispose();
        }
    }
}
