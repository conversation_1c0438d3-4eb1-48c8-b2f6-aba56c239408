using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.ComponentModel;
using System.Linq;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using NAVI.Models;
using NAVI.Services;
using NAVI.Windows;

namespace NAVI
{
    /// <summary>
    /// 数据编辑控件
    /// </summary>
    public partial class DataEditControl : UserControl, INotifyPropertyChanged
    {
        private ObservableCollection<object> _currentData;
        private string _currentDataType;
        private bool _hasUnsavedChanges = false;
        private int _unsavedChangesCount = 0;

        public event PropertyChangedEventHandler PropertyChanged;

        /// <summary>
        /// 当前数据集合
        /// </summary>
        public ObservableCollection<object> CurrentData
        {
            get => _currentData;
            set
            {
                _currentData = value;
                OnPropertyChanged(nameof(CurrentData));
            }
        }

        public DataEditControl()
        {
            InitializeComponent();
            InitializeData();
        }

        /// <summary>
        /// 初始化数据
        /// </summary>
        private void InitializeData()
        {
            CurrentData = new ObservableCollection<object>();
            DataContext = this;
            
            // 默认显示空状态
            EmptyDataState.Visibility = Visibility.Visible;
            DataEditGrid.Visibility = Visibility.Collapsed;
            
            UpdateStatusInfo();
        }

        /// <summary>
        /// 数据类型选择变化事件
        /// </summary>
        private async void DataTypeComboBox_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (DataTypeComboBox.SelectedItem is ComboBoxItem selectedItem)
            {
                var dataType = selectedItem.Tag?.ToString();
                if (!string.IsNullOrEmpty(dataType))
                {
                    await LoadDataByTypeAsync(dataType);
                }
            }
        }

        /// <summary>
        /// 根据类型加载数据
        /// </summary>
        private async Task LoadDataByTypeAsync(string dataType)
        {
            try
            {
                _currentDataType = dataType;
                
                // 显示加载指示器
                LoadingIndicator.Visibility = Visibility.Visible;
                EmptyDataState.Visibility = Visibility.Collapsed;
                
                StatusText.Text = "データ読み込み中...";
                
                // 模拟加载延迟
                await Task.Delay(500);
                
                // 根据数据类型加载相应数据
                switch (dataType)
                {
                    case "BusinessData":
                        await LoadBusinessDataAsync();
                        DataTableTitle.Text = "事業者管理データ一覧";
                        break;
                    case "NationalData":
                        await LoadNationalDataAsync();
                        DataTableTitle.Text = "国保連データ一覧";
                        break;
                    case "ServiceCodeData":
                        await LoadServiceCodeDataAsync();
                        DataTableTitle.Text = "サービスコードデータ一覧";
                        break;
                    case "ReconciliationData":
                        await LoadReconciliationDataAsync();
                        DataTableTitle.Text = "照合結果データ一覧";
                        break;
                    default:
                        CurrentData.Clear();
                        break;
                }
                
                // 设置数据源并显示网格
                DataEditGrid.ItemsSource = CurrentData;
                DataEditGrid.Visibility = Visibility.Visible;
                
                StatusText.Text = "データ読み込み完了";
                LastUpdateText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm");
                
                UpdateStatusInfo();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"データ読み込みに失敗しました：{ex.Message}", "エラー",
                    MessageBoxButton.OK, MessageBoxImage.Error);
                StatusText.Text = "データ読み込みエラー";
            }
            finally
            {
                LoadingIndicator.Visibility = Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 加载事业者数据
        /// </summary>
        private async Task LoadBusinessDataAsync()
        {
            await Task.Run(() =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentData.Clear();
                    
                    // 添加示例数据
                    /*var sampleData = BusinessData.GetSampleData();
                    foreach (var item in sampleData.Take(10))
                    {
                        CurrentData.Add(item);
                    }*/
                    
                    // 动态生成列
                    GenerateColumnsForBusinessData();
                });
            });
        }

        /// <summary>
        /// 加载国保连数据
        /// </summary>
        private async Task LoadNationalDataAsync()
        {
            await Task.Run(() =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentData.Clear();
                    
                    // 添加示例数据
                   /* var sampleData = NationalData.GetSampleData();
                    foreach (var item in sampleData.Take(10))
                    {
                        CurrentData.Add(item);
                    }*/
                    
                    // 动态生成列
                    GenerateColumnsForNationalData();
                });
            });
        }

        /// <summary>
        /// 加载服务代码数据
        /// </summary>
        private async Task LoadServiceCodeDataAsync()
        {
            await Task.Run(() =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentData.Clear();
                    
                    // 添加示例数据
                    /*var sampleData = ServiceCodeData.GetSampleData();
                    foreach (var item in sampleData.Take(10))
                    {
                        CurrentData.Add(item);
                    }*/
                    
                    // 动态生成列
                    GenerateColumnsForServiceCodeData();
                });
            });
        }

        /// <summary>
        /// 加载照合结果数据
        /// </summary>
        private async Task LoadReconciliationDataAsync()
        {
            await Task.Run(() =>
            {
                Application.Current.Dispatcher.Invoke(() =>
                {
                    CurrentData.Clear();
                    
                    // 添加示例照合结果数据
                    for (int i = 1; i <= 5; i++)
                    {
                        CurrentData.Add(new
                        {
                            ID = i,
                            事業者ID = $"P{i:D4}",
                            利用者ID = $"U{i:D6}",
                            照合状態 = i % 3 == 0 ? "不一致" : "一致",
                            照合日時 = DateTime.Now.AddDays(-i).ToString("yyyy-MM-dd HH:mm"),
                            差異内容 = i % 3 == 0 ? "金額相違" : "-"
                        });
                    }
                    
                    // 动态生成列
                    GenerateColumnsForReconciliationData();
                });
            });
        }

        /// <summary>
        /// 为事业者数据生成列
        /// </summary>
        private void GenerateColumnsForBusinessData()
        {
            DataEditGrid.Columns.Clear();
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "事業者編号",
                Binding = new System.Windows.Data.Binding("[事業者編号]"),
                Width = 120
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "事業者名称",
                Binding = new System.Windows.Data.Binding("[事業者名称]"),
                Width = 200
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "郵便番号",
                Binding = new System.Windows.Data.Binding("[郵便番号]"),
                Width = 100
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "住所",
                Binding = new System.Windows.Data.Binding("[住所]"),
                Width = 250
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "電話番号",
                Binding = new System.Windows.Data.Binding("[電話番号]"),
                Width = 120
            });
        }

        /// <summary>
        /// 为国保连数据生成列
        /// </summary>
        private void GenerateColumnsForNationalData()
        {
            DataEditGrid.Columns.Clear();
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "保険者番号",
                Binding = new System.Windows.Data.Binding("[保険者番号]"),
                Width = 120
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "被保険者番号",
                Binding = new System.Windows.Data.Binding("[被保険者番号]"),
                Width = 120
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "氏名",
                Binding = new System.Windows.Data.Binding("[氏名]"),
                Width = 150
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "生年月日",
                Binding = new System.Windows.Data.Binding("[生年月日]"),
                Width = 100
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "性別",
                Binding = new System.Windows.Data.Binding("[性別]"),
                Width = 60
            });
        }

        /// <summary>
        /// 为服务代码数据生成列
        /// </summary>
        private void GenerateColumnsForServiceCodeData()
        {
            DataEditGrid.Columns.Clear();
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "服务代码",
                Binding = new System.Windows.Data.Binding("[服务代码]"),
                Width = 100
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "服务名称",
                Binding = new System.Windows.Data.Binding("[服务名称]"),
                Width = 200
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "服务分类",
                Binding = new System.Windows.Data.Binding("[服务分类]"),
                Width = 120
            });
            
            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "基本报酬",
                Binding = new System.Windows.Data.Binding("[基本报酬]"),
                Width = 100
            });
        }

        /// <summary>
        /// 为照合结果数据生成列
        /// </summary>
        private void GenerateColumnsForReconciliationData()
        {
            DataEditGrid.Columns.Clear();

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "ID",
                Binding = new System.Windows.Data.Binding("ID"),
                Width = 60
            });

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "事業者ID",
                Binding = new System.Windows.Data.Binding("事業者ID"),
                Width = 100
            });

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "利用者ID",
                Binding = new System.Windows.Data.Binding("利用者ID"),
                Width = 100
            });

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "照合状態",
                Binding = new System.Windows.Data.Binding("照合状態"),
                Width = 100
            });

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "照合日時",
                Binding = new System.Windows.Data.Binding("照合日時"),
                Width = 150
            });

            DataEditGrid.Columns.Add(new DataGridTextColumn
            {
                Header = "差異内容",
                Binding = new System.Windows.Data.Binding("差異内容"),
                Width = 150
            });
        }

        /// <summary>
        /// 数据网格选择变化事件
        /// </summary>
        private void DataEditGrid_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            var hasSelection = DataEditGrid.SelectedItem != null;
            EditRecordButton.IsEnabled = hasSelection;
            DeleteRecordButton.IsEnabled = hasSelection;

            UpdateStatusInfo();
        }

        /// <summary>
        /// 添加记录按钮点击事件
        /// </summary>
        private void AddRecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (string.IsNullOrEmpty(_currentDataType))
            {
                MessageBox.Show("まずデータタイプを選択してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 这里应该打开编辑窗口进行新增
            MessageBox.Show("新規レコード追加機能は実装予定です", "お知らせ",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 编辑记录按钮点击事件
        /// </summary>
        private void EditRecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataEditGrid.SelectedItem == null)
            {
                MessageBox.Show("編集するレコードを選択してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 这里应该打开编辑窗口进行编辑
            MessageBox.Show("レコード編集機能は実装予定です", "お知らせ",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 删除记录按钮点击事件
        /// </summary>
        private void DeleteRecordButton_Click(object sender, RoutedEventArgs e)
        {
            if (DataEditGrid.SelectedItem == null)
            {
                MessageBox.Show("削除するレコードを選択してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            var result = MessageBox.Show("選択したレコードを削除しますか？", "削除確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                CurrentData.Remove(DataEditGrid.SelectedItem);
                _hasUnsavedChanges = true;
                _unsavedChangesCount++;
                UpdateStatusInfo();
                StatusText.Text = "レコードを削除しました";
            }
        }

        /// <summary>
        /// 保存更改按钮点击事件
        /// </summary>
        private void SaveChangesButton_Click(object sender, RoutedEventArgs e)
        {
            if (!_hasUnsavedChanges)
            {
                MessageBox.Show("保存する変更がありません", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Information);
                return;
            }

            var result = MessageBox.Show($"{_unsavedChangesCount}個の変更を保存しますか？", "保存確認",
                MessageBoxButton.YesNo, MessageBoxImage.Question);

            if (result == MessageBoxResult.Yes)
            {
                // 这里应该实现实际的保存逻辑
                _hasUnsavedChanges = false;
                _unsavedChangesCount = 0;
                UpdateStatusInfo();
                StatusText.Text = "変更を保存しました";
                LastUpdateText.Text = DateTime.Now.ToString("yyyy-MM-dd HH:mm");

                MessageBox.Show("変更が保存されました", "保存完了",
                    MessageBoxButton.OK, MessageBoxImage.Information);
            }
        }

        /// <summary>
        /// 搜索文本框变化事件
        /// </summary>
        private void SearchTextBox_TextChanged(object sender, TextChangedEventArgs e)
        {
            // 实时搜索功能可以在这里实现
        }

        /// <summary>
        /// 搜索按钮点击事件
        /// </summary>
        private void SearchButton_Click(object sender, RoutedEventArgs e)
        {
            var searchText = SearchTextBox.Text?.Trim();
            if (string.IsNullOrEmpty(searchText))
            {
                MessageBox.Show("検索キーワードを入力してください", "お知らせ",
                    MessageBoxButton.OK, MessageBoxImage.Warning);
                return;
            }

            // 这里应该实现搜索逻辑
            MessageBox.Show($"検索機能は実装予定です：{searchText}", "お知らせ",
                MessageBoxButton.OK, MessageBoxImage.Information);
        }

        /// <summary>
        /// 刷新按钮点击事件
        /// </summary>
        private async void RefreshButton_Click(object sender, RoutedEventArgs e)
        {
            if (_hasUnsavedChanges)
            {
                var result = MessageBox.Show("未保存の変更があります。更新すると変更が失われます。続行しますか？", "更新確認",
                    MessageBoxButton.YesNo, MessageBoxImage.Warning);

                if (result != MessageBoxResult.Yes)
                    return;
            }

            if (!string.IsNullOrEmpty(_currentDataType))
            {
                await LoadDataByTypeAsync(_currentDataType);
                _hasUnsavedChanges = false;
                _unsavedChangesCount = 0;
                UpdateStatusInfo();
            }
        }

        /// <summary>
        /// 更新状态信息
        /// </summary>
        private void UpdateStatusInfo()
        {
            TotalCountText.Text = CurrentData?.Count.ToString() ?? "0";
            SelectedCountText.Text = DataEditGrid.SelectedItems?.Count.ToString() ?? "0";
            UnsavedChangesText.Text = _unsavedChangesCount.ToString();

            SaveChangesButton.IsEnabled = _hasUnsavedChanges;
            AddRecordButton.IsEnabled = !string.IsNullOrEmpty(_currentDataType);
        }

        protected virtual void OnPropertyChanged(string propertyName)
        {
            PropertyChanged?.Invoke(this, new PropertyChangedEventArgs(propertyName));
        }
    }
}
